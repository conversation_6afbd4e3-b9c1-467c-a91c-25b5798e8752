import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../context/AuthContext";

const Header = ({ onToggleSidebar, sidebarCollapsed }) => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [showUserMenu, setShowUserMenu] = useState(false);

  const handleLogout = () => {
    logout();
    navigate("/login");
  };

  const handleProfile = () => {
    navigate("/profile");
    setShowUserMenu(false);
  };

  const handleSettings = () => {
    navigate("/settings");
    setShowUserMenu(false);
  };

  const getRoleColor = (role) => {
    switch (role) {
      case "superadmin":
        return "#ef4444";
      case "admin":
        return "#3b82f6";
      case "agent":
        return "#10b981";
      default:
        return "#64748b";
    }
  };

  return (
    <header className={`header ${sidebarCollapsed ? "sidebar-collapsed" : ""}`}>
      <div className="header-container">
        {/* Sidebar Toggle & Logo */}
        <div className="header-left">
          <button
            className="sidebar-toggle-btn"
            onClick={onToggleSidebar}
            aria-label="Toggle sidebar"
          >
            <svg
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
            >
              <line x1="3" y1="6" x2="21" y2="6"></line>
              <line x1="3" y1="12" x2="21" y2="12"></line>
              <line x1="3" y1="18" x2="21" y2="18"></line>
            </svg>
          </button>
          <div className="header-logo">
            <div className="logo-icon">AP</div>
            <span className="logo-text">Admin Panel</span>
          </div>
        </div>

        {/* User Menu */}
        <div className="header-user">
          <div
            className="user-profile"
            onClick={() => setShowUserMenu(!showUserMenu)}
          >
            <div className="user-avatar">
              {user?.name?.charAt(0)?.toUpperCase()}
            </div>
            <div className="user-info">
              <div className="user-name">{user?.name}</div>
              <div
                className="user-role"
                style={{ color: getRoleColor(user?.role) }}
              >
                {user?.role}
              </div>
            </div>
            <div className="dropdown-arrow">▼</div>
          </div>

          {/* Dropdown Menu */}
          {showUserMenu && (
            <div className="user-dropdown">
              <button className="dropdown-item" onClick={handleProfile}>
                <span className="dropdown-icon">👤</span>
                Profile
              </button>
              <button className="dropdown-item" onClick={handleSettings}>
                <span className="dropdown-icon">⚙️</span>
                Settings
              </button>
              <div className="dropdown-divider"></div>
              <button className="dropdown-item logout" onClick={handleLogout}>
                <span className="dropdown-icon">🚪</span>
                Logout
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Click outside to close dropdown */}
      {showUserMenu && (
        <div
          className="dropdown-overlay"
          onClick={() => setShowUserMenu(false)}
        ></div>
      )}
    </header>
  );
};

export default Header;
