// Variables
:root {
  --primary-color: #3b82f6;
  --primary-dark: #2563eb;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --background-color: #f8fafc;
  --surface-color: #ffffff;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border-color: #e2e8f0;
  --sidebar-bg: #1e293b;
  --sidebar-text: #cbd5e1;
  --sidebar-active: #3b82f6;
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

// Reset and base styles
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--background-color);
  color: var(--text-primary);
  line-height: 1.6;
  overflow-x: hidden;
}

// Ensure no conflicts with default styles
#root {
  min-height: 100vh;
}

// Layout
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-body {
  flex: 1;
  display: flex;
  min-height: calc(100vh - 70px);
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

// Header
.header {
  background: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow);
  height: 70px;
  z-index: 999;
  flex-shrink: 0;
  position: fixed;
  top: 0;
  left: 280px;
  right: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.sidebar-collapsed {
    left: 80px;
  }

  .header-container {
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 1rem;

    .sidebar-toggle-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border: none;
      background: transparent;
      color: var(--text-secondary);
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: var(--background-color);
        color: var(--primary-color);
      }
    }
  }

  .header-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 700;
    color: var(--text-primary);

    .logo-icon {
      width: 40px;
      height: 40px;
      background: var(--primary-color);
      color: white;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 1.1rem;
    }

    .logo-text {
      font-size: 1.25rem;
    }
  }

// Sidebar - Enhanced with higher specificity
aside.sidebar {
  width: 280px !important;
  height: 100vh !important;
  background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%) !important;
  color: #cbd5e1 !important;
  display: flex !important;
  flex-direction: column !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 2px 0 15px rgba(0, 0, 0, 0.15) !important;
  flex-shrink: 0 !important;
  border-right: 1px solid rgba(255, 255, 255, 0.1) !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  z-index: 1000 !important;

  &.collapsed {
    width: 80px;

    .nav-link {
      justify-content: center;
      padding: 1rem 0.75rem;

      .nav-label {
        display: none;
      }

      .nav-icon {
        margin: 0;
      }
    }
  }

  // Force styles to override any conflicting CSS
  &.sidebar {
    background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%) !important;
    color: #cbd5e1 !important;
  }



  .sidebar-nav {
    flex: 1 !important;
    padding: 2rem 0 !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;

    /* Custom scrollbar */
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.05);
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.2);
      border-radius: 2px;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
      }
    }

    .nav-section {
      .nav-list {
        list-style: none !important;
        padding: 0 !important;
        margin: 0 !important;

        .nav-item {
          margin: 0 1.25rem 0.75rem !important;

          .nav-link {
            display: flex !important;
            align-items: center !important;
            gap: 1rem !important;
            padding: 1rem 1.25rem !important;
            border-radius: 12px !important;
            text-decoration: none !important;
            color: #cbd5e1 !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            position: relative !important;
            font-weight: 500 !important;
            background: transparent !important;
            font-size: 0.875rem !important;
            border: 1px solid transparent !important;

            &:hover {
              background: rgba(59, 130, 246, 0.15) !important;
              color: white !important;
              transform: translateX(4px) !important;
              box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2) !important;
              border-color: rgba(59, 130, 246, 0.3) !important;
            }

            &.active {
              background: linear-gradient(135deg, var(--primary-color) 0%, #2563eb 100%) !important;
              color: white !important;
              box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4) !important;
              transform: translateX(0) !important;
              border-color: rgba(255, 255, 255, 0.2) !important;

              &::before {
                content: '';
                position: absolute;
                left: -1.25rem;
                top: 50%;
                transform: translateY(-50%);
                width: 4px;
                height: 32px;
                background: rgba(255, 255, 255, 0.9);
                border-radius: 0 2px 2px 0;
              }

              .nav-icon svg {
                transform: scale(1.1);
              }
            }

            .nav-icon {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 24px;
              height: 24px;
              flex-shrink: 0;
              margin-right: 0.5rem;

              svg {
                width: 20px;
                height: 20px;
                transition: all 0.3s ease;
                stroke-width: 2;
              }
            }

            .nav-label {
              font-size: 0.875rem;
              font-weight: 500;
              flex: 1;
              white-space: nowrap;
              letter-spacing: 0.025em;
              color: inherit;
            }

            // Ensure proper styling
            &.nav-link {
              background: transparent !important;
              color: #cbd5e1 !important;

              &:hover {
                background: rgba(59, 130, 246, 0.15) !important;
                color: white !important;
              }

              &.active {
                background: linear-gradient(135deg, var(--primary-color) 0%, #2563eb 100%) !important;
                color: white !important;
              }
            }
          }
        }
      }
    }
  }
}

// Sidebar overlay for mobile
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 997;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;

  &.active {
    opacity: 1;
    visibility: visible;
  }
}

  .header-user {
    position: relative;

    .user-profile {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 0.5rem;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: var(--background-color);
      }

      .user-avatar {
        width: 40px;
        height: 40px;
        background: var(--primary-color);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 1rem;
      }

      .user-info {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .user-name {
          font-weight: 600;
          color: var(--text-primary);
          font-size: 0.875rem;
          line-height: 1.2;
        }

        .user-role {
          font-size: 0.75rem;
          font-weight: 500;
          text-transform: capitalize;
          line-height: 1.2;
        }
      }

      .dropdown-arrow {
        font-size: 0.75rem;
        color: var(--text-secondary);
        transition: transform 0.2s ease;
      }
    }

    .user-dropdown {
      position: absolute;
      top: 100%;
      right: 0;
      background: var(--surface-color);
      border: 1px solid var(--border-color);
      border-radius: 8px;
      box-shadow: var(--shadow-lg);
      min-width: 180px;
      padding: 0.5rem 0;
      margin-top: 0.5rem;

      .dropdown-item {
        width: 100%;
        padding: 0.75rem 1rem;
        border: none;
        background: none;
        text-align: left;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        color: var(--text-primary);
        font-size: 0.875rem;
        transition: all 0.2s ease;

        &:hover {
          background: var(--background-color);
        }

        &.logout {
          color: var(--error-color);

          &:hover {
            background: rgba(239, 68, 68, 0.1);
          }
        }

        .dropdown-icon {
          font-size: 1rem;
        }
      }

      .dropdown-divider {
        height: 1px;
        background: var(--border-color);
        margin: 0.5rem 0;
      }
    }
  }

  .dropdown-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
  }
}

// Content area
.content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  background: var(--background-color);
  margin-left: 280px;
  margin-top: 70px;
  min-height: calc(100vh - 70px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.sidebar-collapsed {
    margin-left: 80px;
  }
}

// Cards
.card {
  background: var(--surface-color);
  border-radius: 12px;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
  overflow: hidden;

  .card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .card-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0;
    }
  }

  .card-body {
    padding: 1.5rem;
  }
}

// Buttons
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  gap: 0.5rem;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &.btn-primary {
    background: var(--primary-color);
    color: white;

    &:hover:not(:disabled) {
      background: var(--primary-dark);
      transform: translateY(-1px);
    }
  }

  &.btn-secondary {
    background: var(--secondary-color);
    color: white;

    &:hover:not(:disabled) {
      background: #475569;
      transform: translateY(-1px);
    }
  }

  &.btn-success {
    background: var(--success-color);
    color: white;

    &:hover:not(:disabled) {
      background: #059669;
      transform: translateY(-1px);
    }
  }

  &.btn-danger {
    background: var(--error-color);
    color: white;

    &:hover:not(:disabled) {
      background: #dc2626;
      transform: translateY(-1px);
    }
  }

  &.btn-outline {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);

    &:hover:not(:disabled) {
      background: var(--primary-color);
      color: white;
    }
  }

  &.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
  }

  &.btn-lg {
    padding: 1rem 2rem;
    font-size: 1rem;
  }
}

// Forms
.form-group {
  margin-bottom: 1.5rem;

  .form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
  }

  .form-control {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: var(--surface-color);

    &:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    &.error {
      border-color: var(--error-color);
    }
  }

  .form-error {
    color: var(--error-color);
    font-size: 0.75rem;
    margin-top: 0.25rem;
  }
}

// Responsive
@media (max-width: 768px) {
  .app-body {
    position: relative;
  }

  .sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1001;
    transform: translateX(-100%);
    width: 280px !important;

    &.open {
      transform: translateX(0);
    }

    &.collapsed {
      width: 280px !important;
    }

    .nav-link {
      padding: 1rem 1.25rem !important;
      justify-content: flex-start !important;

      .nav-label {
        display: block !important;
      }

      .nav-icon {
        margin-right: 0.5rem !important;
      }
    }
  }

  .header {
    position: fixed;
    left: 0 !important;
    right: 0;
    top: 0;
    z-index: 1000;

    .header-container {
      padding: 0 1rem;
    }

    .header-logo .logo-text {
      display: none;
    }
  }

  .content {
    padding: 1rem;
    margin-left: 0 !important;
    margin-top: 70px;
  }

  .card {
    .card-body {
      padding: 1rem;
    }
  }
}

@media (max-width: 1024px) {
  .sidebar {
    &.collapsed {
      width: 70px;

      .nav-link {
        padding: 1rem 0.5rem;
        justify-content: center;

        .nav-icon {
          margin-right: 0;
        }

        .nav-label {
          display: none;
        }

        &::before {
          left: -0.5rem !important;
        }
      }
    }
  }
}
