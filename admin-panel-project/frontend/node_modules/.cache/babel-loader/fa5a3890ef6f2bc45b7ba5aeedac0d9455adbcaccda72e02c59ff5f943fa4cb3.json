{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/context/AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useEffect } from \"react\";\nimport { authAPI } from \"../services/api\";\n\n// Initial state\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst initialState = {\n  user: null,\n  token: null,\n  isAuthenticated: false,\n  isLoading: true,\n  error: null\n};\n\n// Action types\nconst AUTH_ACTIONS = {\n  LOGIN_START: \"LOGIN_START\",\n  LOGIN_SUCCESS: \"LOGIN_SUCCESS\",\n  LOGIN_FAILURE: \"LOGIN_FAILURE\",\n  LOGOUT: \"LOGOUT\",\n  LOAD_USER_START: \"LOAD_USER_START\",\n  LOAD_USER_SUCCESS: \"LOAD_USER_SUCCESS\",\n  LOAD_USER_FAILURE: \"LOAD_USER_FAILURE\",\n  CLEAR_ERROR: \"CLEAR_ERROR\",\n  UPDATE_USER: \"UPDATE_USER\"\n};\n\n// Reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case AUTH_ACTIONS.LOGIN_START:\n      return {\n        ...state,\n        isLoading: true,\n        error: null\n      };\n    case AUTH_ACTIONS.LOGIN_SUCCESS:\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      };\n    case AUTH_ACTIONS.LOGIN_FAILURE:\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload\n      };\n    case AUTH_ACTIONS.LOGOUT:\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null\n      };\n    case AUTH_ACTIONS.LOAD_USER_START:\n      return {\n        ...state,\n        isLoading: true\n      };\n    case AUTH_ACTIONS.LOAD_USER_SUCCESS:\n      return {\n        ...state,\n        user: action.payload,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      };\n    case AUTH_ACTIONS.LOAD_USER_FAILURE:\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload\n      };\n    case AUTH_ACTIONS.CLEAR_ERROR:\n      return {\n        ...state,\n        error: null\n      };\n    case AUTH_ACTIONS.UPDATE_USER:\n      return {\n        ...state,\n        user: {\n          ...state.user,\n          ...action.payload\n        }\n      };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AuthContext = /*#__PURE__*/createContext();\n\n// Auth provider component\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Load current user\n  const loadUser = async () => {\n    try {\n      dispatch({\n        type: AUTH_ACTIONS.LOAD_USER_START\n      });\n      const response = await authAPI.getCurrentUser();\n      if (response.success) {\n        dispatch({\n          type: AUTH_ACTIONS.LOAD_USER_SUCCESS,\n          payload: response.user\n        });\n\n        // Update localStorage\n        localStorage.setItem(\"user\", JSON.stringify(response.user));\n      } else {\n        throw new Error(response.message || \"Failed to load user\");\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error(\"Load user error:\", error);\n      dispatch({\n        type: AUTH_ACTIONS.LOAD_USER_FAILURE,\n        payload: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || \"Failed to load user\"\n      });\n\n      // Clear invalid token\n      localStorage.removeItem(\"token\");\n      localStorage.removeItem(\"user\");\n    }\n  };\n\n  // Logout function\n  const logout = () => {\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"user\");\n    dispatch({\n      type: AUTH_ACTIONS.LOGOUT\n    });\n  };\n\n  // Load user on app start\n  useEffect(() => {\n    const token = localStorage.getItem(\"token\");\n    const user = localStorage.getItem(\"user\");\n    if (token && user) {\n      try {\n        const parsedUser = JSON.parse(user);\n        dispatch({\n          type: AUTH_ACTIONS.LOGIN_SUCCESS,\n          payload: {\n            user: parsedUser,\n            token\n          }\n        });\n\n        // Verify token is still valid\n        loadUser();\n      } catch (error) {\n        console.error(\"Error parsing stored user:\", error);\n        logout();\n      }\n    } else {\n      dispatch({\n        type: AUTH_ACTIONS.LOAD_USER_FAILURE,\n        payload: null\n      });\n    }\n  }, []);\n\n  // Login function\n  const login = async credentials => {\n    try {\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_START\n      });\n      const response = await authAPI.login(credentials);\n      if (response.success) {\n        const {\n          user,\n          token\n        } = response;\n\n        // Store in localStorage\n        localStorage.setItem(\"token\", token);\n        localStorage.setItem(\"user\", JSON.stringify(user));\n        dispatch({\n          type: AUTH_ACTIONS.LOGIN_SUCCESS,\n          payload: {\n            user,\n            token\n          }\n        });\n        return {\n          success: true\n        };\n      } else {\n        throw new Error(response.message || \"Login failed\");\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      const errorMessage = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || error.message || \"Login failed\";\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_FAILURE,\n        payload: errorMessage\n      });\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  };\n\n  // Update user data\n  const updateUser = userData => {\n    dispatch({\n      type: AUTH_ACTIONS.UPDATE_USER,\n      payload: userData\n    });\n\n    // Update localStorage\n    const updatedUser = {\n      ...state.user,\n      ...userData\n    };\n    localStorage.setItem(\"user\", JSON.stringify(updatedUser));\n  };\n\n  // Clear error\n  const clearError = () => {\n    dispatch({\n      type: AUTH_ACTIONS.CLEAR_ERROR\n    });\n  };\n\n  // Context value\n  const value = {\n    ...state,\n    login,\n    logout,\n    loadUser,\n    updateUser,\n    clearError\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 238,\n    columnNumber: 10\n  }, this);\n};\n\n// Custom hook to use auth context\n_s(AuthProvider, \"bgCdjuTOmPdSBRwTap80EFd9Y3U=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error(\"useAuth must be used within an AuthProvider\");\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "authAPI", "jsxDEV", "_jsxDEV", "initialState", "user", "token", "isAuthenticated", "isLoading", "error", "AUTH_ACTIONS", "LOGIN_START", "LOGIN_SUCCESS", "LOGIN_FAILURE", "LOGOUT", "LOAD_USER_START", "LOAD_USER_SUCCESS", "LOAD_USER_FAILURE", "CLEAR_ERROR", "UPDATE_USER", "authReducer", "state", "action", "type", "payload", "AuthContext", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "dispatch", "loadUser", "response", "getCurrentUser", "success", "localStorage", "setItem", "JSON", "stringify", "Error", "message", "_error$response", "_error$response$data", "console", "data", "removeItem", "logout", "getItem", "parsedUser", "parse", "login", "credentials", "_error$response2", "_error$response2$data", "errorMessage", "updateUser", "userData", "updatedUser", "clearError", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "$RefreshReg$"], "sources": ["/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/context/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useEffect } from \"react\";\nimport { authAPI } from \"../services/api\";\n\n// Initial state\nconst initialState = {\n  user: null,\n  token: null,\n  isAuthenticated: false,\n  isLoading: true,\n  error: null,\n};\n\n// Action types\nconst AUTH_ACTIONS = {\n  LOGIN_START: \"LOGIN_START\",\n  LOGIN_SUCCESS: \"LOGIN_SUCCESS\",\n  LOGIN_FAILURE: \"LOGIN_FAILURE\",\n  LOGOUT: \"LOGOUT\",\n  LOAD_USER_START: \"LOAD_USER_START\",\n  LOAD_USER_SUCCESS: \"LOAD_USER_SUCCESS\",\n  LOAD_USER_FAILURE: \"LOAD_USER_FAILURE\",\n  CLEAR_ERROR: \"CLEAR_ERROR\",\n  UPDATE_USER: \"UPDATE_USER\",\n};\n\n// Reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case AUTH_ACTIONS.LOGIN_START:\n      return {\n        ...state,\n        isLoading: true,\n        error: null,\n      };\n\n    case AUTH_ACTIONS.LOGIN_SUCCESS:\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null,\n      };\n\n    case AUTH_ACTIONS.LOGIN_FAILURE:\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload,\n      };\n\n    case AUTH_ACTIONS.LOGOUT:\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null,\n      };\n\n    case AUTH_ACTIONS.LOAD_USER_START:\n      return {\n        ...state,\n        isLoading: true,\n      };\n\n    case AUTH_ACTIONS.LOAD_USER_SUCCESS:\n      return {\n        ...state,\n        user: action.payload,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null,\n      };\n\n    case AUTH_ACTIONS.LOAD_USER_FAILURE:\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload,\n      };\n\n    case AUTH_ACTIONS.CLEAR_ERROR:\n      return {\n        ...state,\n        error: null,\n      };\n\n    case AUTH_ACTIONS.UPDATE_USER:\n      return {\n        ...state,\n        user: { ...state.user, ...action.payload },\n      };\n\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AuthContext = createContext();\n\n// Auth provider component\nexport const AuthProvider = ({ children }) => {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Load current user\n  const loadUser = async () => {\n    try {\n      dispatch({ type: AUTH_ACTIONS.LOAD_USER_START });\n\n      const response = await authAPI.getCurrentUser();\n\n      if (response.success) {\n        dispatch({\n          type: AUTH_ACTIONS.LOAD_USER_SUCCESS,\n          payload: response.user,\n        });\n\n        // Update localStorage\n        localStorage.setItem(\"user\", JSON.stringify(response.user));\n      } else {\n        throw new Error(response.message || \"Failed to load user\");\n      }\n    } catch (error) {\n      console.error(\"Load user error:\", error);\n      dispatch({\n        type: AUTH_ACTIONS.LOAD_USER_FAILURE,\n        payload: error.response?.data?.message || \"Failed to load user\",\n      });\n\n      // Clear invalid token\n      localStorage.removeItem(\"token\");\n      localStorage.removeItem(\"user\");\n    }\n  };\n\n  // Logout function\n  const logout = () => {\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"user\");\n    dispatch({ type: AUTH_ACTIONS.LOGOUT });\n  };\n\n  // Load user on app start\n  useEffect(() => {\n    const token = localStorage.getItem(\"token\");\n    const user = localStorage.getItem(\"user\");\n\n    if (token && user) {\n      try {\n        const parsedUser = JSON.parse(user);\n        dispatch({\n          type: AUTH_ACTIONS.LOGIN_SUCCESS,\n          payload: { user: parsedUser, token },\n        });\n\n        // Verify token is still valid\n        loadUser();\n      } catch (error) {\n        console.error(\"Error parsing stored user:\", error);\n        logout();\n      }\n    } else {\n      dispatch({ type: AUTH_ACTIONS.LOAD_USER_FAILURE, payload: null });\n    }\n  }, []);\n\n  // Login function\n  const login = async (credentials) => {\n    try {\n      dispatch({ type: AUTH_ACTIONS.LOGIN_START });\n\n      const response = await authAPI.login(credentials);\n\n      if (response.success) {\n        const { user, token } = response;\n\n        // Store in localStorage\n        localStorage.setItem(\"token\", token);\n        localStorage.setItem(\"user\", JSON.stringify(user));\n\n        dispatch({\n          type: AUTH_ACTIONS.LOGIN_SUCCESS,\n          payload: { user, token },\n        });\n\n        return { success: true };\n      } else {\n        throw new Error(response.message || \"Login failed\");\n      }\n    } catch (error) {\n      const errorMessage =\n        error.response?.data?.message || error.message || \"Login failed\";\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_FAILURE,\n        payload: errorMessage,\n      });\n      return { success: false, error: errorMessage };\n    }\n  };\n\n  // Update user data\n  const updateUser = (userData) => {\n    dispatch({\n      type: AUTH_ACTIONS.UPDATE_USER,\n      payload: userData,\n    });\n\n    // Update localStorage\n    const updatedUser = { ...state.user, ...userData };\n    localStorage.setItem(\"user\", JSON.stringify(updatedUser));\n  };\n\n  // Clear error\n  const clearError = () => {\n    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });\n  };\n\n  // Context value\n  const value = {\n    ...state,\n    login,\n    logout,\n    loadUser,\n    updateUser,\n    clearError,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n\n// Custom hook to use auth context\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error(\"useAuth must be used within an AuthProvider\");\n  }\n  return context;\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC/E,SAASC,OAAO,QAAQ,iBAAiB;;AAEzC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAG;EACnBC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,IAAI;EACXC,eAAe,EAAE,KAAK;EACtBC,SAAS,EAAE,IAAI;EACfC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,MAAMC,YAAY,GAAG;EACnBC,WAAW,EAAE,aAAa;EAC1BC,aAAa,EAAE,eAAe;EAC9BC,aAAa,EAAE,eAAe;EAC9BC,MAAM,EAAE,QAAQ;EAChBC,eAAe,EAAE,iBAAiB;EAClCC,iBAAiB,EAAE,mBAAmB;EACtCC,iBAAiB,EAAE,mBAAmB;EACtCC,WAAW,EAAE,aAAa;EAC1BC,WAAW,EAAE;AACf,CAAC;;AAED;AACA,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACrC,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKb,YAAY,CAACC,WAAW;MAC3B,OAAO;QACL,GAAGU,KAAK;QACRb,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKC,YAAY,CAACE,aAAa;MAC7B,OAAO;QACL,GAAGS,KAAK;QACRhB,IAAI,EAAEiB,MAAM,CAACE,OAAO,CAACnB,IAAI;QACzBC,KAAK,EAAEgB,MAAM,CAACE,OAAO,CAAClB,KAAK;QAC3BC,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKC,YAAY,CAACG,aAAa;MAC7B,OAAO;QACL,GAAGQ,KAAK;QACRhB,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXC,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAEa,MAAM,CAACE;MAChB,CAAC;IAEH,KAAKd,YAAY,CAACI,MAAM;MACtB,OAAO;QACL,GAAGO,KAAK;QACRhB,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXC,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKC,YAAY,CAACK,eAAe;MAC/B,OAAO;QACL,GAAGM,KAAK;QACRb,SAAS,EAAE;MACb,CAAC;IAEH,KAAKE,YAAY,CAACM,iBAAiB;MACjC,OAAO;QACL,GAAGK,KAAK;QACRhB,IAAI,EAAEiB,MAAM,CAACE,OAAO;QACpBjB,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKC,YAAY,CAACO,iBAAiB;MACjC,OAAO;QACL,GAAGI,KAAK;QACRhB,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXC,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAEa,MAAM,CAACE;MAChB,CAAC;IAEH,KAAKd,YAAY,CAACQ,WAAW;MAC3B,OAAO;QACL,GAAGG,KAAK;QACRZ,KAAK,EAAE;MACT,CAAC;IAEH,KAAKC,YAAY,CAACS,WAAW;MAC3B,OAAO;QACL,GAAGE,KAAK;QACRhB,IAAI,EAAE;UAAE,GAAGgB,KAAK,CAAChB,IAAI;UAAE,GAAGiB,MAAM,CAACE;QAAQ;MAC3C,CAAC;IAEH;MACE,OAAOH,KAAK;EAChB;AACF,CAAC;;AAED;AACA,MAAMI,WAAW,gBAAG5B,aAAa,CAAC,CAAC;;AAEnC;AACA,OAAO,MAAM6B,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACP,KAAK,EAAEQ,QAAQ,CAAC,GAAG9B,UAAU,CAACqB,WAAW,EAAEhB,YAAY,CAAC;;EAE/D;EACA,MAAM0B,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFD,QAAQ,CAAC;QAAEN,IAAI,EAAEb,YAAY,CAACK;MAAgB,CAAC,CAAC;MAEhD,MAAMgB,QAAQ,GAAG,MAAM9B,OAAO,CAAC+B,cAAc,CAAC,CAAC;MAE/C,IAAID,QAAQ,CAACE,OAAO,EAAE;QACpBJ,QAAQ,CAAC;UACPN,IAAI,EAAEb,YAAY,CAACM,iBAAiB;UACpCQ,OAAO,EAAEO,QAAQ,CAAC1B;QACpB,CAAC,CAAC;;QAEF;QACA6B,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACN,QAAQ,CAAC1B,IAAI,CAAC,CAAC;MAC7D,CAAC,MAAM;QACL,MAAM,IAAIiC,KAAK,CAACP,QAAQ,CAACQ,OAAO,IAAI,qBAAqB,CAAC;MAC5D;IACF,CAAC,CAAC,OAAO9B,KAAK,EAAE;MAAA,IAAA+B,eAAA,EAAAC,oBAAA;MACdC,OAAO,CAACjC,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxCoB,QAAQ,CAAC;QACPN,IAAI,EAAEb,YAAY,CAACO,iBAAiB;QACpCO,OAAO,EAAE,EAAAgB,eAAA,GAAA/B,KAAK,CAACsB,QAAQ,cAAAS,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBG,IAAI,cAAAF,oBAAA,uBAApBA,oBAAA,CAAsBF,OAAO,KAAI;MAC5C,CAAC,CAAC;;MAEF;MACAL,YAAY,CAACU,UAAU,CAAC,OAAO,CAAC;MAChCV,YAAY,CAACU,UAAU,CAAC,MAAM,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMC,MAAM,GAAGA,CAAA,KAAM;IACnBX,YAAY,CAACU,UAAU,CAAC,OAAO,CAAC;IAChCV,YAAY,CAACU,UAAU,CAAC,MAAM,CAAC;IAC/Bf,QAAQ,CAAC;MAAEN,IAAI,EAAEb,YAAY,CAACI;IAAO,CAAC,CAAC;EACzC,CAAC;;EAED;EACAd,SAAS,CAAC,MAAM;IACd,MAAMM,KAAK,GAAG4B,YAAY,CAACY,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMzC,IAAI,GAAG6B,YAAY,CAACY,OAAO,CAAC,MAAM,CAAC;IAEzC,IAAIxC,KAAK,IAAID,IAAI,EAAE;MACjB,IAAI;QACF,MAAM0C,UAAU,GAAGX,IAAI,CAACY,KAAK,CAAC3C,IAAI,CAAC;QACnCwB,QAAQ,CAAC;UACPN,IAAI,EAAEb,YAAY,CAACE,aAAa;UAChCY,OAAO,EAAE;YAAEnB,IAAI,EAAE0C,UAAU;YAAEzC;UAAM;QACrC,CAAC,CAAC;;QAEF;QACAwB,QAAQ,CAAC,CAAC;MACZ,CAAC,CAAC,OAAOrB,KAAK,EAAE;QACdiC,OAAO,CAACjC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDoC,MAAM,CAAC,CAAC;MACV;IACF,CAAC,MAAM;MACLhB,QAAQ,CAAC;QAAEN,IAAI,EAAEb,YAAY,CAACO,iBAAiB;QAAEO,OAAO,EAAE;MAAK,CAAC,CAAC;IACnE;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMyB,KAAK,GAAG,MAAOC,WAAW,IAAK;IACnC,IAAI;MACFrB,QAAQ,CAAC;QAAEN,IAAI,EAAEb,YAAY,CAACC;MAAY,CAAC,CAAC;MAE5C,MAAMoB,QAAQ,GAAG,MAAM9B,OAAO,CAACgD,KAAK,CAACC,WAAW,CAAC;MAEjD,IAAInB,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAM;UAAE5B,IAAI;UAAEC;QAAM,CAAC,GAAGyB,QAAQ;;QAEhC;QACAG,YAAY,CAACC,OAAO,CAAC,OAAO,EAAE7B,KAAK,CAAC;QACpC4B,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAAChC,IAAI,CAAC,CAAC;QAElDwB,QAAQ,CAAC;UACPN,IAAI,EAAEb,YAAY,CAACE,aAAa;UAChCY,OAAO,EAAE;YAAEnB,IAAI;YAAEC;UAAM;QACzB,CAAC,CAAC;QAEF,OAAO;UAAE2B,OAAO,EAAE;QAAK,CAAC;MAC1B,CAAC,MAAM;QACL,MAAM,IAAIK,KAAK,CAACP,QAAQ,CAACQ,OAAO,IAAI,cAAc,CAAC;MACrD;IACF,CAAC,CAAC,OAAO9B,KAAK,EAAE;MAAA,IAAA0C,gBAAA,EAAAC,qBAAA;MACd,MAAMC,YAAY,GAChB,EAAAF,gBAAA,GAAA1C,KAAK,CAACsB,QAAQ,cAAAoB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBR,IAAI,cAAAS,qBAAA,uBAApBA,qBAAA,CAAsBb,OAAO,KAAI9B,KAAK,CAAC8B,OAAO,IAAI,cAAc;MAClEV,QAAQ,CAAC;QACPN,IAAI,EAAEb,YAAY,CAACG,aAAa;QAChCW,OAAO,EAAE6B;MACX,CAAC,CAAC;MACF,OAAO;QAAEpB,OAAO,EAAE,KAAK;QAAExB,KAAK,EAAE4C;MAAa,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMC,UAAU,GAAIC,QAAQ,IAAK;IAC/B1B,QAAQ,CAAC;MACPN,IAAI,EAAEb,YAAY,CAACS,WAAW;MAC9BK,OAAO,EAAE+B;IACX,CAAC,CAAC;;IAEF;IACA,MAAMC,WAAW,GAAG;MAAE,GAAGnC,KAAK,CAAChB,IAAI;MAAE,GAAGkD;IAAS,CAAC;IAClDrB,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACmB,WAAW,CAAC,CAAC;EAC3D,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB5B,QAAQ,CAAC;MAAEN,IAAI,EAAEb,YAAY,CAACQ;IAAY,CAAC,CAAC;EAC9C,CAAC;;EAED;EACA,MAAMwC,KAAK,GAAG;IACZ,GAAGrC,KAAK;IACR4B,KAAK;IACLJ,MAAM;IACNf,QAAQ;IACRwB,UAAU;IACVG;EACF,CAAC;EAED,oBAAOtD,OAAA,CAACsB,WAAW,CAACkC,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA/B,QAAA,EAAEA;EAAQ;IAAAiC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAuB,CAAC;AAC9E,CAAC;;AAED;AAAAnC,EAAA,CAjIaF,YAAY;AAAAsC,EAAA,GAAZtC,YAAY;AAkIzB,OAAO,MAAMuC,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAMC,OAAO,GAAGrE,UAAU,CAAC2B,WAAW,CAAC;EACvC,IAAI,CAAC0C,OAAO,EAAE;IACZ,MAAM,IAAI7B,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAO6B,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAQpB,eAAexC,WAAW;AAAC,IAAAuC,EAAA;AAAAI,YAAA,CAAAJ,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}