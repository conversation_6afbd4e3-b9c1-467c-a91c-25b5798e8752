{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/components/Header.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useAuth } from \"../context/AuthContext\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = ({\n  onToggleSidebar\n}) => {\n  _s();\n  var _user$name, _user$name$charAt;\n  const {\n    user,\n    logout\n  } = useAuth();\n  const navigate = useNavigate();\n  const [showUserMenu, setShowUserMenu] = useState(false);\n  const handleLogout = () => {\n    logout();\n    navigate(\"/login\");\n  };\n  const handleProfile = () => {\n    navigate(\"/profile\");\n    setShowUserMenu(false);\n  };\n  const handleSettings = () => {\n    navigate(\"/settings\");\n    setShowUserMenu(false);\n  };\n  const getRoleColor = role => {\n    switch (role) {\n      case \"superadmin\":\n        return \"#ef4444\";\n      case \"admin\":\n        return \"#3b82f6\";\n      case \"agent\":\n        return \"#10b981\";\n      default:\n        return \"#64748b\";\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"header\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-logo\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo-icon\",\n          children: \"AP\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"logo-text\",\n          children: \"Admin Panel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"header-nav\",\n        children: filteredMenuItems.map(item => /*#__PURE__*/_jsxDEV(NavLink, {\n          to: item.path,\n          className: ({\n            isActive\n          }) => `nav-item ${isActive ? \"active\" : \"\"}`,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"nav-icon\",\n            children: item.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"nav-label\",\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 15\n          }, this)]\n        }, item.path, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-user\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-profile\",\n          onClick: () => setShowUserMenu(!showUserMenu),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-avatar\",\n            children: user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : (_user$name$charAt = _user$name.charAt(0)) === null || _user$name$charAt === void 0 ? void 0 : _user$name$charAt.toUpperCase()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"user-name\",\n              children: user === null || user === void 0 ? void 0 : user.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"user-role\",\n              style: {\n                color: getRoleColor(user === null || user === void 0 ? void 0 : user.role)\n              },\n              children: user === null || user === void 0 ? void 0 : user.role\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"dropdown-arrow\",\n            children: \"\\u25BC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), showUserMenu && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-dropdown\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"dropdown-item\",\n            onClick: handleProfile,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"dropdown-icon\",\n              children: \"\\uD83D\\uDC64\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 17\n            }, this), \"Profile\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"dropdown-item\",\n            onClick: handleSettings,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"dropdown-icon\",\n              children: \"\\u2699\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 17\n            }, this), \"Settings\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"dropdown-divider\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"dropdown-item logout\",\n            onClick: handleLogout,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"dropdown-icon\",\n              children: \"\\uD83D\\uDEAA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 17\n            }, this), \"Logout\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this), showUserMenu && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dropdown-overlay\",\n      onClick: () => setShowUserMenu(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"l/dNgIzDD5aHvjW6n1QIsKhZ5BI=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useAuth", "jsxDEV", "_jsxDEV", "Header", "onToggleSidebar", "_s", "_user$name", "_user$name$charAt", "user", "logout", "navigate", "showUserMenu", "setShowUserMenu", "handleLogout", "handleProfile", "handleSettings", "getRoleColor", "role", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "filteredMenuItems", "map", "item", "NavLink", "to", "path", "isActive", "icon", "label", "onClick", "name", "char<PERSON>t", "toUpperCase", "style", "color", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/components/Header.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useAuth } from \"../context/AuthContext\";\n\nconst Header = ({ onToggleSidebar }) => {\n  const { user, logout } = useAuth();\n  const navigate = useNavigate();\n  const [showUserMenu, setShowUserMenu] = useState(false);\n\n  const handleLogout = () => {\n    logout();\n    navigate(\"/login\");\n  };\n\n  const handleProfile = () => {\n    navigate(\"/profile\");\n    setShowUserMenu(false);\n  };\n\n  const handleSettings = () => {\n    navigate(\"/settings\");\n    setShowUserMenu(false);\n  };\n\n  const getRoleColor = (role) => {\n    switch (role) {\n      case \"superadmin\":\n        return \"#ef4444\";\n      case \"admin\":\n        return \"#3b82f6\";\n      case \"agent\":\n        return \"#10b981\";\n      default:\n        return \"#64748b\";\n    }\n  };\n\n  return (\n    <header className=\"header\">\n      <div className=\"header-container\">\n        {/* Logo */}\n        <div className=\"header-logo\">\n          <div className=\"logo-icon\">AP</div>\n          <span className=\"logo-text\">Admin Panel</span>\n        </div>\n\n        {/* Navigation Menu */}\n        <nav className=\"header-nav\">\n          {filteredMenuItems.map((item) => (\n            <NavLink\n              key={item.path}\n              to={item.path}\n              className={({ isActive }) =>\n                `nav-item ${isActive ? \"active\" : \"\"}`\n              }\n            >\n              <span className=\"nav-icon\">{item.icon}</span>\n              <span className=\"nav-label\">{item.label}</span>\n            </NavLink>\n          ))}\n        </nav>\n\n        {/* User Menu */}\n        <div className=\"header-user\">\n          <div\n            className=\"user-profile\"\n            onClick={() => setShowUserMenu(!showUserMenu)}\n          >\n            <div className=\"user-avatar\">\n              {user?.name?.charAt(0)?.toUpperCase()}\n            </div>\n            <div className=\"user-info\">\n              <div className=\"user-name\">{user?.name}</div>\n              <div\n                className=\"user-role\"\n                style={{ color: getRoleColor(user?.role) }}\n              >\n                {user?.role}\n              </div>\n            </div>\n            <div className=\"dropdown-arrow\">▼</div>\n          </div>\n\n          {/* Dropdown Menu */}\n          {showUserMenu && (\n            <div className=\"user-dropdown\">\n              <button className=\"dropdown-item\" onClick={handleProfile}>\n                <span className=\"dropdown-icon\">👤</span>\n                Profile\n              </button>\n              <button className=\"dropdown-item\" onClick={handleSettings}>\n                <span className=\"dropdown-icon\">⚙️</span>\n                Settings\n              </button>\n              <div className=\"dropdown-divider\"></div>\n              <button className=\"dropdown-item logout\" onClick={handleLogout}>\n                <span className=\"dropdown-icon\">🚪</span>\n                Logout\n              </button>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Click outside to close dropdown */}\n      {showUserMenu && (\n        <div\n          className=\"dropdown-overlay\"\n          onClick={() => setShowUserMenu(false)}\n        ></div>\n      )}\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,MAAM,GAAGA,CAAC;EAAEC;AAAgB,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,UAAA,EAAAC,iBAAA;EACtC,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGT,OAAO,CAAC,CAAC;EAClC,MAAMU,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACY,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMe,YAAY,GAAGA,CAAA,KAAM;IACzBJ,MAAM,CAAC,CAAC;IACRC,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMI,aAAa,GAAGA,CAAA,KAAM;IAC1BJ,QAAQ,CAAC,UAAU,CAAC;IACpBE,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMG,cAAc,GAAGA,CAAA,KAAM;IAC3BL,QAAQ,CAAC,WAAW,CAAC;IACrBE,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMI,YAAY,GAAIC,IAAI,IAAK;IAC7B,QAAQA,IAAI;MACV,KAAK,YAAY;QACf,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,oBACEf,OAAA;IAAQgB,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBACxBjB,OAAA;MAAKgB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAE/BjB,OAAA;QAAKgB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BjB,OAAA;UAAKgB,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnCrB,OAAA;UAAMgB,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eAGNrB,OAAA;QAAKgB,SAAS,EAAC,YAAY;QAAAC,QAAA,EACxBK,iBAAiB,CAACC,GAAG,CAAEC,IAAI,iBAC1BxB,OAAA,CAACyB,OAAO;UAENC,EAAE,EAAEF,IAAI,CAACG,IAAK;UACdX,SAAS,EAAEA,CAAC;YAAEY;UAAS,CAAC,KACtB,YAAYA,QAAQ,GAAG,QAAQ,GAAG,EAAE,EACrC;UAAAX,QAAA,gBAEDjB,OAAA;YAAMgB,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAEO,IAAI,CAACK;UAAI;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7CrB,OAAA;YAAMgB,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAEO,IAAI,CAACM;UAAK;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAP1CG,IAAI,CAACG,IAAI;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQP,CACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNrB,OAAA;QAAKgB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BjB,OAAA;UACEgB,SAAS,EAAC,cAAc;UACxBe,OAAO,EAAEA,CAAA,KAAMrB,eAAe,CAAC,CAACD,YAAY,CAAE;UAAAQ,QAAA,gBAE9CjB,OAAA;YAAKgB,SAAS,EAAC,aAAa;YAAAC,QAAA,EACzBX,IAAI,aAAJA,IAAI,wBAAAF,UAAA,GAAJE,IAAI,CAAE0B,IAAI,cAAA5B,UAAA,wBAAAC,iBAAA,GAAVD,UAAA,CAAY6B,MAAM,CAAC,CAAC,CAAC,cAAA5B,iBAAA,uBAArBA,iBAAA,CAAuB6B,WAAW,CAAC;UAAC;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACNrB,OAAA;YAAKgB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBjB,OAAA;cAAKgB,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAEX,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0B;YAAI;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7CrB,OAAA;cACEgB,SAAS,EAAC,WAAW;cACrBmB,KAAK,EAAE;gBAAEC,KAAK,EAAEtB,YAAY,CAACR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,IAAI;cAAE,CAAE;cAAAE,QAAA,EAE1CX,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNrB,OAAA;YAAKgB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,EAGLZ,YAAY,iBACXT,OAAA;UAAKgB,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BjB,OAAA;YAAQgB,SAAS,EAAC,eAAe;YAACe,OAAO,EAAEnB,aAAc;YAAAK,QAAA,gBACvDjB,OAAA;cAAMgB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,WAE3C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrB,OAAA;YAAQgB,SAAS,EAAC,eAAe;YAACe,OAAO,EAAElB,cAAe;YAAAI,QAAA,gBACxDjB,OAAA;cAAMgB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,YAE3C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrB,OAAA;YAAKgB,SAAS,EAAC;UAAkB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxCrB,OAAA;YAAQgB,SAAS,EAAC,sBAAsB;YAACe,OAAO,EAAEpB,YAAa;YAAAM,QAAA,gBAC7DjB,OAAA;cAAMgB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,UAE3C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLZ,YAAY,iBACXT,OAAA;MACEgB,SAAS,EAAC,kBAAkB;MAC5Be,OAAO,EAAEA,CAAA,KAAMrB,eAAe,CAAC,KAAK;IAAE;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAAClB,EAAA,CA7GIF,MAAM;EAAA,QACeH,OAAO,EACfD,WAAW;AAAA;AAAAwC,EAAA,GAFxBpC,MAAM;AA+GZ,eAAeA,MAAM;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}