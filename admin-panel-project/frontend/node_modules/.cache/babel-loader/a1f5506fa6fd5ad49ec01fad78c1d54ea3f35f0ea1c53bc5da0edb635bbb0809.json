{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Dashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { useAuth } from \"../context/AuthContext\";\nimport { usersAPI } from \"../services/api\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [stats, setStats] = useState({\n    totalUsers: 0,\n    activeUsers: 0,\n    adminUsers: 0,\n    agentUsers: 0\n  });\n  const [recentUsers, setRecentUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n\n      // Load users data for stats\n      const response = await usersAPI.getUsers({\n        limit: 10\n      });\n      if (response.success) {\n        const users = response.data;\n\n        // Calculate stats\n        const totalUsers = response.pagination.total;\n        const activeUsers = users.filter(u => u.isActive).length;\n        const adminUsers = users.filter(u => u.role === \"admin\").length;\n        const agentUsers = users.filter(u => u.role === \"agent\").length;\n        setStats({\n          totalUsers,\n          activeUsers,\n          adminUsers,\n          agentUsers\n        });\n        setRecentUsers(users.slice(0, 5));\n      }\n    } catch (error) {\n      console.error(\"Error loading dashboard data:\", error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString(\"en-US\", {\n      year: \"numeric\",\n      month: \"short\",\n      day: \"numeric\"\n    });\n  };\n  const getGreeting = () => {\n    const hour = new Date().getHours();\n    if (hour < 12) return \"Good morning\";\n    if (hour < 18) return \"Good afternoon\";\n    return \"Good evening\";\n  };\n  const getRoleColor = role => {\n    switch (role) {\n      case \"superadmin\":\n        return \"#ef4444\";\n      case \"admin\":\n        return \"#3b82f6\";\n      case \"agent\":\n        return \"#10b981\";\n      default:\n        return \"#64748b\";\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: \"flex\",\n          justifyContent: \"center\",\n          alignItems: \"center\",\n          height: \"200px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Loading dashboard...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      style: {\n        marginBottom: \"2rem\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            fontSize: \"2rem\",\n            fontWeight: \"700\",\n            marginBottom: \"0.5rem\",\n            color: \"var(--text-primary)\"\n          },\n          children: [getGreeting(), \", \", user === null || user === void 0 ? void 0 : user.name, \"! \\uD83D\\uDC4B\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: \"var(--text-secondary)\",\n            fontSize: \"1.1rem\"\n          },\n          children: \"Welcome to your admin dashboard. Here's what's happening today.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: \"grid\",\n        gridTemplateColumns: \"repeat(auto-fit, minmax(280px, 1fr))\",\n        gap: \"2rem\",\n        marginBottom: \"3rem\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"space-between\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: \"var(--text-secondary)\",\n                  fontSize: \"0.875rem\",\n                  marginBottom: \"0.5rem\"\n                },\n                children: \"Total Users\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: \"2rem\",\n                  fontWeight: \"700\",\n                  color: \"var(--text-primary)\",\n                  margin: 0\n                },\n                children: stats.totalUsers\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: \"2rem\",\n                opacity: 0.6\n              },\n              children: \"\\uD83D\\uDC65\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"space-between\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: \"var(--text-secondary)\",\n                  fontSize: \"0.875rem\",\n                  marginBottom: \"0.5rem\"\n                },\n                children: \"Active Users\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: \"2rem\",\n                  fontWeight: \"700\",\n                  color: \"var(--success-color)\",\n                  margin: 0\n                },\n                children: stats.activeUsers\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: \"2rem\",\n                opacity: 0.6\n              },\n              children: \"\\u2705\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"space-between\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: \"var(--text-secondary)\",\n                  fontSize: \"0.875rem\",\n                  marginBottom: \"0.5rem\"\n                },\n                children: \"Administrators\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: \"2rem\",\n                  fontWeight: \"700\",\n                  color: \"var(--primary-color)\",\n                  margin: 0\n                },\n                children: stats.adminUsers\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: \"2rem\",\n                opacity: 0.6\n              },\n              children: \"\\uD83D\\uDC51\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"space-between\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: \"var(--text-secondary)\",\n                  fontSize: \"0.875rem\",\n                  marginBottom: \"0.5rem\"\n                },\n                children: \"Agents\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: \"2rem\",\n                  fontWeight: \"700\",\n                  color: \"var(--warning-color)\",\n                  margin: 0\n                },\n                children: stats.agentUsers\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: \"2rem\",\n                opacity: 0.6\n              },\n              children: \"\\uD83C\\uDFAF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: \"grid\",\n        gridTemplateColumns: \"repeat(auto-fit, minmax(400px, 1fr))\",\n        gap: \"2rem\",\n        marginBottom: \"3rem\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"card-title\",\n            children: \"Quick Actions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"grid\",\n              gap: \"1rem\"\n            },\n            children: [((user === null || user === void 0 ? void 0 : user.role) === \"superadmin\" || (user === null || user === void 0 ? void 0 : user.role) === \"admin\") && /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              onClick: () => window.location.href = \"/users\",\n              style: {\n                justifyContent: \"flex-start\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  marginRight: \"0.75rem\"\n                },\n                children: \"\\uD83D\\uDC65\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 19\n              }, this), \"Manage Users\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-secondary\",\n              onClick: () => window.location.href = \"/analytics\",\n              style: {\n                justifyContent: \"flex-start\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  marginRight: \"0.75rem\"\n                },\n                children: \"\\uD83D\\uDCC8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 17\n              }, this), \"View Analytics\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-secondary\",\n              onClick: () => window.location.href = \"/reports\",\n              style: {\n                justifyContent: \"flex-start\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  marginRight: \"0.75rem\"\n                },\n                children: \"\\uD83D\\uDCCB\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this), \"Generate Reports\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-secondary\",\n              onClick: () => window.location.href = \"/profile\",\n              style: {\n                justifyContent: \"flex-start\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  marginRight: \"0.75rem\"\n                },\n                children: \"\\uD83D\\uDC64\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this), \"Update Profile\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"card-title\",\n            children: \"System Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"grid\",\n              gap: \"1rem\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"flex\",\n                justifyContent: \"space-between\",\n                alignItems: \"center\",\n                padding: \"0.75rem\",\n                background: \"var(--background-color)\",\n                borderRadius: \"8px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: \"0.75rem\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: \"1.25rem\"\n                  },\n                  children: \"\\uD83D\\uDFE2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: \"500\"\n                  },\n                  children: \"Server Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: \"var(--success-color)\",\n                  fontWeight: \"600\"\n                },\n                children: \"Online\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"flex\",\n                justifyContent: \"space-between\",\n                alignItems: \"center\",\n                padding: \"0.75rem\",\n                background: \"var(--background-color)\",\n                borderRadius: \"8px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: \"0.75rem\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: \"1.25rem\"\n                  },\n                  children: \"\\uD83D\\uDFE2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: \"500\"\n                  },\n                  children: \"Database\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: \"var(--success-color)\",\n                  fontWeight: \"600\"\n                },\n                children: \"Connected\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"flex\",\n                justifyContent: \"space-between\",\n                alignItems: \"center\",\n                padding: \"0.75rem\",\n                background: \"var(--background-color)\",\n                borderRadius: \"8px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: \"0.75rem\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: \"1.25rem\"\n                  },\n                  children: \"\\uD83D\\uDCCA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: \"500\"\n                  },\n                  children: \"Last Backup\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: \"var(--text-secondary)\",\n                  fontWeight: \"500\"\n                },\n                children: \"2 hours ago\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"flex\",\n                justifyContent: \"space-between\",\n                alignItems: \"center\",\n                padding: \"0.75rem\",\n                background: \"var(--background-color)\",\n                borderRadius: \"8px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: \"0.75rem\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: \"1.25rem\"\n                  },\n                  children: \"\\u26A1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: \"500\"\n                  },\n                  children: \"Performance\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: \"var(--success-color)\",\n                  fontWeight: \"600\"\n                },\n                children: \"Excellent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 7\n    }, this), ((user === null || user === void 0 ? void 0 : user.role) === \"superadmin\" || (user === null || user === void 0 ? void 0 : user.role) === \"admin\") && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"card-title\",\n          children: \"Recent Users\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary btn-sm\",\n          onClick: () => window.location.href = \"/users\",\n          children: \"View All\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 441,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: recentUsers.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            overflowX: \"auto\"\n          },\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            style: {\n              width: \"100%\",\n              borderCollapse: \"collapse\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                style: {\n                  borderBottom: \"2px solid var(--border-color)\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    padding: \"1rem\",\n                    textAlign: \"left\",\n                    color: \"var(--text-secondary)\",\n                    fontWeight: \"600\"\n                  },\n                  children: \"User\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    padding: \"1rem\",\n                    textAlign: \"left\",\n                    color: \"var(--text-secondary)\",\n                    fontWeight: \"600\"\n                  },\n                  children: \"Role\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    padding: \"1rem\",\n                    textAlign: \"left\",\n                    color: \"var(--text-secondary)\",\n                    fontWeight: \"600\"\n                  },\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    padding: \"1rem\",\n                    textAlign: \"left\",\n                    color: \"var(--text-secondary)\",\n                    fontWeight: \"600\"\n                  },\n                  children: \"Created\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: recentUsers.map(user => /*#__PURE__*/_jsxDEV(\"tr\", {\n                style: {\n                  borderBottom: \"1px solid var(--border-color)\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: \"1rem\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontWeight: \"500\",\n                        color: \"var(--text-primary)\"\n                      },\n                      children: user.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 510,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: \"0.875rem\",\n                        color: \"var(--text-secondary)\"\n                      },\n                      children: user.email\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 518,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 509,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 508,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: \"1rem\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      padding: \"0.25rem 0.75rem\",\n                      borderRadius: \"9999px\",\n                      fontSize: \"0.75rem\",\n                      fontWeight: \"500\",\n                      backgroundColor: getRoleColor(user.role) + \"20\",\n                      color: getRoleColor(user.role)\n                    },\n                    children: user.role\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 529,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 528,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: \"1rem\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      padding: \"0.25rem 0.75rem\",\n                      borderRadius: \"9999px\",\n                      fontSize: \"0.75rem\",\n                      fontWeight: \"500\",\n                      backgroundColor: user.isActive ? \"var(--success-color)20\" : \"var(--error-color)20\",\n                      color: user.isActive ? \"var(--success-color)\" : \"var(--error-color)\"\n                    },\n                    children: user.isActive ? \"Active\" : \"Inactive\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 543,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: \"1rem\",\n                    color: \"var(--text-secondary)\"\n                  },\n                  children: formatDate(user.createdAt)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 560,\n                  columnNumber: 25\n                }, this)]\n              }, user._id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 500,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: \"center\",\n            padding: \"2rem\",\n            color: \"var(--text-secondary)\"\n          },\n          children: \"No users found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 574,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 450,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 440,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 98,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"YLfZlQs3IKB8j1EVIfJ5gzaGuoQ=\", false, function () {\n  return [useAuth];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "usersAPI", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "user", "stats", "setStats", "totalUsers", "activeUsers", "adminUsers", "agentUsers", "recentUsers", "setRecentUsers", "loading", "setLoading", "loadDashboardData", "response", "getUsers", "limit", "success", "users", "data", "pagination", "total", "filter", "u", "isActive", "length", "role", "slice", "error", "console", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "getGreeting", "hour", "getHours", "getRoleColor", "className", "children", "style", "display", "justifyContent", "alignItems", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginBottom", "fontSize", "fontWeight", "color", "name", "gridTemplateColumns", "gap", "margin", "opacity", "onClick", "window", "location", "href", "marginRight", "padding", "background", "borderRadius", "overflowX", "width", "borderCollapse", "borderBottom", "textAlign", "map", "email", "backgroundColor", "createdAt", "_id", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Dashboard.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport { useAuth } from \"../context/AuthContext\";\nimport { usersAPI } from \"../services/api\";\n\nconst Dashboard = () => {\n  const { user } = useAuth();\n  const [stats, setStats] = useState({\n    totalUsers: 0,\n    activeUsers: 0,\n    adminUsers: 0,\n    agentUsers: 0,\n  });\n  const [recentUsers, setRecentUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n\n      // Load users data for stats\n      const response = await usersAPI.getUsers({ limit: 10 });\n\n      if (response.success) {\n        const users = response.data;\n\n        // Calculate stats\n        const totalUsers = response.pagination.total;\n        const activeUsers = users.filter((u) => u.isActive).length;\n        const adminUsers = users.filter((u) => u.role === \"admin\").length;\n        const agentUsers = users.filter((u) => u.role === \"agent\").length;\n\n        setStats({\n          totalUsers,\n          activeUsers,\n          adminUsers,\n          agentUsers,\n        });\n\n        setRecentUsers(users.slice(0, 5));\n      }\n    } catch (error) {\n      console.error(\"Error loading dashboard data:\", error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString(\"en-US\", {\n      year: \"numeric\",\n      month: \"short\",\n      day: \"numeric\",\n    });\n  };\n\n  const getGreeting = () => {\n    const hour = new Date().getHours();\n    if (hour < 12) return \"Good morning\";\n    if (hour < 18) return \"Good afternoon\";\n    return \"Good evening\";\n  };\n\n  const getRoleColor = (role) => {\n    switch (role) {\n      case \"superadmin\":\n        return \"#ef4444\";\n      case \"admin\":\n        return \"#3b82f6\";\n      case \"agent\":\n        return \"#10b981\";\n      default:\n        return \"#64748b\";\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"content\">\n        <div\n          style={{\n            display: \"flex\",\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            height: \"200px\",\n          }}\n        >\n          <div>Loading dashboard...</div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      {/* Welcome Section */}\n      <div className=\"card\" style={{ marginBottom: \"2rem\" }}>\n        <div className=\"card-body\">\n          <h1\n            style={{\n              fontSize: \"2rem\",\n              fontWeight: \"700\",\n              marginBottom: \"0.5rem\",\n              color: \"var(--text-primary)\",\n            }}\n          >\n            {getGreeting()}, {user?.name}! 👋\n          </h1>\n          <p style={{ color: \"var(--text-secondary)\", fontSize: \"1.1rem\" }}>\n            Welcome to your admin dashboard. Here's what's happening today.\n          </p>\n        </div>\n      </div>\n\n      {/* Stats Cards */}\n      <div\n        style={{\n          display: \"grid\",\n          gridTemplateColumns: \"repeat(auto-fit, minmax(280px, 1fr))\",\n          gap: \"2rem\",\n          marginBottom: \"3rem\",\n        }}\n      >\n        <div className=\"card\">\n          <div className=\"card-body\">\n            <div\n              style={{\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n              }}\n            >\n              <div>\n                <p\n                  style={{\n                    color: \"var(--text-secondary)\",\n                    fontSize: \"0.875rem\",\n                    marginBottom: \"0.5rem\",\n                  }}\n                >\n                  Total Users\n                </p>\n                <p\n                  style={{\n                    fontSize: \"2rem\",\n                    fontWeight: \"700\",\n                    color: \"var(--text-primary)\",\n                    margin: 0,\n                  }}\n                >\n                  {stats.totalUsers}\n                </p>\n              </div>\n              <div style={{ fontSize: \"2rem\", opacity: 0.6 }}>👥</div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"card\">\n          <div className=\"card-body\">\n            <div\n              style={{\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n              }}\n            >\n              <div>\n                <p\n                  style={{\n                    color: \"var(--text-secondary)\",\n                    fontSize: \"0.875rem\",\n                    marginBottom: \"0.5rem\",\n                  }}\n                >\n                  Active Users\n                </p>\n                <p\n                  style={{\n                    fontSize: \"2rem\",\n                    fontWeight: \"700\",\n                    color: \"var(--success-color)\",\n                    margin: 0,\n                  }}\n                >\n                  {stats.activeUsers}\n                </p>\n              </div>\n              <div style={{ fontSize: \"2rem\", opacity: 0.6 }}>✅</div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"card\">\n          <div className=\"card-body\">\n            <div\n              style={{\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n              }}\n            >\n              <div>\n                <p\n                  style={{\n                    color: \"var(--text-secondary)\",\n                    fontSize: \"0.875rem\",\n                    marginBottom: \"0.5rem\",\n                  }}\n                >\n                  Administrators\n                </p>\n                <p\n                  style={{\n                    fontSize: \"2rem\",\n                    fontWeight: \"700\",\n                    color: \"var(--primary-color)\",\n                    margin: 0,\n                  }}\n                >\n                  {stats.adminUsers}\n                </p>\n              </div>\n              <div style={{ fontSize: \"2rem\", opacity: 0.6 }}>👑</div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"card\">\n          <div className=\"card-body\">\n            <div\n              style={{\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n              }}\n            >\n              <div>\n                <p\n                  style={{\n                    color: \"var(--text-secondary)\",\n                    fontSize: \"0.875rem\",\n                    marginBottom: \"0.5rem\",\n                  }}\n                >\n                  Agents\n                </p>\n                <p\n                  style={{\n                    fontSize: \"2rem\",\n                    fontWeight: \"700\",\n                    color: \"var(--warning-color)\",\n                    margin: 0,\n                  }}\n                >\n                  {stats.agentUsers}\n                </p>\n              </div>\n              <div style={{ fontSize: \"2rem\", opacity: 0.6 }}>🎯</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Actions & System Status */}\n      <div\n        style={{\n          display: \"grid\",\n          gridTemplateColumns: \"repeat(auto-fit, minmax(400px, 1fr))\",\n          gap: \"2rem\",\n          marginBottom: \"3rem\",\n        }}\n      >\n        {/* Quick Actions */}\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h3 className=\"card-title\">Quick Actions</h3>\n          </div>\n          <div className=\"card-body\">\n            <div style={{ display: \"grid\", gap: \"1rem\" }}>\n              {(user?.role === \"superadmin\" || user?.role === \"admin\") && (\n                <button\n                  className=\"btn btn-primary\"\n                  onClick={() => (window.location.href = \"/users\")}\n                  style={{ justifyContent: \"flex-start\" }}\n                >\n                  <span style={{ marginRight: \"0.75rem\" }}>👥</span>\n                  Manage Users\n                </button>\n              )}\n              <button\n                className=\"btn btn-secondary\"\n                onClick={() => (window.location.href = \"/analytics\")}\n                style={{ justifyContent: \"flex-start\" }}\n              >\n                <span style={{ marginRight: \"0.75rem\" }}>📈</span>\n                View Analytics\n              </button>\n              <button\n                className=\"btn btn-secondary\"\n                onClick={() => (window.location.href = \"/reports\")}\n                style={{ justifyContent: \"flex-start\" }}\n              >\n                <span style={{ marginRight: \"0.75rem\" }}>📋</span>\n                Generate Reports\n              </button>\n              <button\n                className=\"btn btn-secondary\"\n                onClick={() => (window.location.href = \"/profile\")}\n                style={{ justifyContent: \"flex-start\" }}\n              >\n                <span style={{ marginRight: \"0.75rem\" }}>👤</span>\n                Update Profile\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* System Status */}\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h3 className=\"card-title\">System Status</h3>\n          </div>\n          <div className=\"card-body\">\n            <div style={{ display: \"grid\", gap: \"1rem\" }}>\n              <div\n                style={{\n                  display: \"flex\",\n                  justifyContent: \"space-between\",\n                  alignItems: \"center\",\n                  padding: \"0.75rem\",\n                  background: \"var(--background-color)\",\n                  borderRadius: \"8px\",\n                }}\n              >\n                <div\n                  style={{\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: \"0.75rem\",\n                  }}\n                >\n                  <span style={{ fontSize: \"1.25rem\" }}>🟢</span>\n                  <span style={{ fontWeight: \"500\" }}>Server Status</span>\n                </div>\n                <span\n                  style={{ color: \"var(--success-color)\", fontWeight: \"600\" }}\n                >\n                  Online\n                </span>\n              </div>\n              <div\n                style={{\n                  display: \"flex\",\n                  justifyContent: \"space-between\",\n                  alignItems: \"center\",\n                  padding: \"0.75rem\",\n                  background: \"var(--background-color)\",\n                  borderRadius: \"8px\",\n                }}\n              >\n                <div\n                  style={{\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: \"0.75rem\",\n                  }}\n                >\n                  <span style={{ fontSize: \"1.25rem\" }}>🟢</span>\n                  <span style={{ fontWeight: \"500\" }}>Database</span>\n                </div>\n                <span\n                  style={{ color: \"var(--success-color)\", fontWeight: \"600\" }}\n                >\n                  Connected\n                </span>\n              </div>\n              <div\n                style={{\n                  display: \"flex\",\n                  justifyContent: \"space-between\",\n                  alignItems: \"center\",\n                  padding: \"0.75rem\",\n                  background: \"var(--background-color)\",\n                  borderRadius: \"8px\",\n                }}\n              >\n                <div\n                  style={{\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: \"0.75rem\",\n                  }}\n                >\n                  <span style={{ fontSize: \"1.25rem\" }}>📊</span>\n                  <span style={{ fontWeight: \"500\" }}>Last Backup</span>\n                </div>\n                <span\n                  style={{ color: \"var(--text-secondary)\", fontWeight: \"500\" }}\n                >\n                  2 hours ago\n                </span>\n              </div>\n              <div\n                style={{\n                  display: \"flex\",\n                  justifyContent: \"space-between\",\n                  alignItems: \"center\",\n                  padding: \"0.75rem\",\n                  background: \"var(--background-color)\",\n                  borderRadius: \"8px\",\n                }}\n              >\n                <div\n                  style={{\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: \"0.75rem\",\n                  }}\n                >\n                  <span style={{ fontSize: \"1.25rem\" }}>⚡</span>\n                  <span style={{ fontWeight: \"500\" }}>Performance</span>\n                </div>\n                <span\n                  style={{ color: \"var(--success-color)\", fontWeight: \"600\" }}\n                >\n                  Excellent\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Recent Users */}\n      {(user?.role === \"superadmin\" || user?.role === \"admin\") && (\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h2 className=\"card-title\">Recent Users</h2>\n            <button\n              className=\"btn btn-primary btn-sm\"\n              onClick={() => (window.location.href = \"/users\")}\n            >\n              View All\n            </button>\n          </div>\n          <div className=\"card-body\">\n            {recentUsers.length > 0 ? (\n              <div style={{ overflowX: \"auto\" }}>\n                <table style={{ width: \"100%\", borderCollapse: \"collapse\" }}>\n                  <thead>\n                    <tr\n                      style={{ borderBottom: \"2px solid var(--border-color)\" }}\n                    >\n                      <th\n                        style={{\n                          padding: \"1rem\",\n                          textAlign: \"left\",\n                          color: \"var(--text-secondary)\",\n                          fontWeight: \"600\",\n                        }}\n                      >\n                        User\n                      </th>\n                      <th\n                        style={{\n                          padding: \"1rem\",\n                          textAlign: \"left\",\n                          color: \"var(--text-secondary)\",\n                          fontWeight: \"600\",\n                        }}\n                      >\n                        Role\n                      </th>\n                      <th\n                        style={{\n                          padding: \"1rem\",\n                          textAlign: \"left\",\n                          color: \"var(--text-secondary)\",\n                          fontWeight: \"600\",\n                        }}\n                      >\n                        Status\n                      </th>\n                      <th\n                        style={{\n                          padding: \"1rem\",\n                          textAlign: \"left\",\n                          color: \"var(--text-secondary)\",\n                          fontWeight: \"600\",\n                        }}\n                      >\n                        Created\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {recentUsers.map((user) => (\n                      <tr\n                        key={user._id}\n                        style={{\n                          borderBottom: \"1px solid var(--border-color)\",\n                        }}\n                      >\n                        <td style={{ padding: \"1rem\" }}>\n                          <div>\n                            <div\n                              style={{\n                                fontWeight: \"500\",\n                                color: \"var(--text-primary)\",\n                              }}\n                            >\n                              {user.name}\n                            </div>\n                            <div\n                              style={{\n                                fontSize: \"0.875rem\",\n                                color: \"var(--text-secondary)\",\n                              }}\n                            >\n                              {user.email}\n                            </div>\n                          </div>\n                        </td>\n                        <td style={{ padding: \"1rem\" }}>\n                          <span\n                            style={{\n                              padding: \"0.25rem 0.75rem\",\n                              borderRadius: \"9999px\",\n                              fontSize: \"0.75rem\",\n                              fontWeight: \"500\",\n                              backgroundColor: getRoleColor(user.role) + \"20\",\n                              color: getRoleColor(user.role),\n                            }}\n                          >\n                            {user.role}\n                          </span>\n                        </td>\n                        <td style={{ padding: \"1rem\" }}>\n                          <span\n                            style={{\n                              padding: \"0.25rem 0.75rem\",\n                              borderRadius: \"9999px\",\n                              fontSize: \"0.75rem\",\n                              fontWeight: \"500\",\n                              backgroundColor: user.isActive\n                                ? \"var(--success-color)20\"\n                                : \"var(--error-color)20\",\n                              color: user.isActive\n                                ? \"var(--success-color)\"\n                                : \"var(--error-color)\",\n                            }}\n                          >\n                            {user.isActive ? \"Active\" : \"Inactive\"}\n                          </span>\n                        </td>\n                        <td\n                          style={{\n                            padding: \"1rem\",\n                            color: \"var(--text-secondary)\",\n                          }}\n                        >\n                          {formatDate(user.createdAt)}\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            ) : (\n              <div\n                style={{\n                  textAlign: \"center\",\n                  padding: \"2rem\",\n                  color: \"var(--text-secondary)\",\n                }}\n              >\n                No users found\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,QAAQ,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGN,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACO,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAC;IACjCW,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACdkB,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFD,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAME,QAAQ,GAAG,MAAMjB,QAAQ,CAACkB,QAAQ,CAAC;QAAEC,KAAK,EAAE;MAAG,CAAC,CAAC;MAEvD,IAAIF,QAAQ,CAACG,OAAO,EAAE;QACpB,MAAMC,KAAK,GAAGJ,QAAQ,CAACK,IAAI;;QAE3B;QACA,MAAMd,UAAU,GAAGS,QAAQ,CAACM,UAAU,CAACC,KAAK;QAC5C,MAAMf,WAAW,GAAGY,KAAK,CAACI,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACC,QAAQ,CAAC,CAACC,MAAM;QAC1D,MAAMlB,UAAU,GAAGW,KAAK,CAACI,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACG,IAAI,KAAK,OAAO,CAAC,CAACD,MAAM;QACjE,MAAMjB,UAAU,GAAGU,KAAK,CAACI,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACG,IAAI,KAAK,OAAO,CAAC,CAACD,MAAM;QAEjErB,QAAQ,CAAC;UACPC,UAAU;UACVC,WAAW;UACXC,UAAU;UACVC;QACF,CAAC,CAAC;QAEFE,cAAc,CAACQ,KAAK,CAACS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnC;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkB,UAAU,GAAIC,UAAU,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,IAAI,GAAG,IAAIN,IAAI,CAAC,CAAC,CAACO,QAAQ,CAAC,CAAC;IAClC,IAAID,IAAI,GAAG,EAAE,EAAE,OAAO,cAAc;IACpC,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,gBAAgB;IACtC,OAAO,cAAc;EACvB,CAAC;EAED,MAAME,YAAY,GAAId,IAAI,IAAK;IAC7B,QAAQA,IAAI;MACV,KAAK,YAAY;QACf,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,IAAIf,OAAO,EAAE;IACX,oBACEZ,OAAA;MAAK0C,SAAS,EAAC,SAAS;MAAAC,QAAA,eACtB3C,OAAA;QACE4C,KAAK,EAAE;UACLC,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,QAAQ;UACxBC,UAAU,EAAE,QAAQ;UACpBC,MAAM,EAAE;QACV,CAAE;QAAAL,QAAA,eAEF3C,OAAA;UAAA2C,QAAA,EAAK;QAAoB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEpD,OAAA;IAAA2C,QAAA,gBAEE3C,OAAA;MAAK0C,SAAS,EAAC,MAAM;MAACE,KAAK,EAAE;QAAES,YAAY,EAAE;MAAO,CAAE;MAAAV,QAAA,eACpD3C,OAAA;QAAK0C,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB3C,OAAA;UACE4C,KAAK,EAAE;YACLU,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjBF,YAAY,EAAE,QAAQ;YACtBG,KAAK,EAAE;UACT,CAAE;UAAAb,QAAA,GAEDL,WAAW,CAAC,CAAC,EAAC,IAAE,EAACnC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsD,IAAI,EAAC,gBAC/B;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLpD,OAAA;UAAG4C,KAAK,EAAE;YAAEY,KAAK,EAAE,uBAAuB;YAAEF,QAAQ,EAAE;UAAS,CAAE;UAAAX,QAAA,EAAC;QAElE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpD,OAAA;MACE4C,KAAK,EAAE;QACLC,OAAO,EAAE,MAAM;QACfa,mBAAmB,EAAE,sCAAsC;QAC3DC,GAAG,EAAE,MAAM;QACXN,YAAY,EAAE;MAChB,CAAE;MAAAV,QAAA,gBAEF3C,OAAA;QAAK0C,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB3C,OAAA;UAAK0C,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB3C,OAAA;YACE4C,KAAK,EAAE;cACLC,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE;YAClB,CAAE;YAAAH,QAAA,gBAEF3C,OAAA;cAAA2C,QAAA,gBACE3C,OAAA;gBACE4C,KAAK,EAAE;kBACLY,KAAK,EAAE,uBAAuB;kBAC9BF,QAAQ,EAAE,UAAU;kBACpBD,YAAY,EAAE;gBAChB,CAAE;gBAAAV,QAAA,EACH;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJpD,OAAA;gBACE4C,KAAK,EAAE;kBACLU,QAAQ,EAAE,MAAM;kBAChBC,UAAU,EAAE,KAAK;kBACjBC,KAAK,EAAE,qBAAqB;kBAC5BI,MAAM,EAAE;gBACV,CAAE;gBAAAjB,QAAA,EAEDvC,KAAK,CAACE;cAAU;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNpD,OAAA;cAAK4C,KAAK,EAAE;gBAAEU,QAAQ,EAAE,MAAM;gBAAEO,OAAO,EAAE;cAAI,CAAE;cAAAlB,QAAA,EAAC;YAAE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpD,OAAA;QAAK0C,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB3C,OAAA;UAAK0C,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB3C,OAAA;YACE4C,KAAK,EAAE;cACLC,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE;YAClB,CAAE;YAAAH,QAAA,gBAEF3C,OAAA;cAAA2C,QAAA,gBACE3C,OAAA;gBACE4C,KAAK,EAAE;kBACLY,KAAK,EAAE,uBAAuB;kBAC9BF,QAAQ,EAAE,UAAU;kBACpBD,YAAY,EAAE;gBAChB,CAAE;gBAAAV,QAAA,EACH;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJpD,OAAA;gBACE4C,KAAK,EAAE;kBACLU,QAAQ,EAAE,MAAM;kBAChBC,UAAU,EAAE,KAAK;kBACjBC,KAAK,EAAE,sBAAsB;kBAC7BI,MAAM,EAAE;gBACV,CAAE;gBAAAjB,QAAA,EAEDvC,KAAK,CAACG;cAAW;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNpD,OAAA;cAAK4C,KAAK,EAAE;gBAAEU,QAAQ,EAAE,MAAM;gBAAEO,OAAO,EAAE;cAAI,CAAE;cAAAlB,QAAA,EAAC;YAAC;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpD,OAAA;QAAK0C,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB3C,OAAA;UAAK0C,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB3C,OAAA;YACE4C,KAAK,EAAE;cACLC,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE;YAClB,CAAE;YAAAH,QAAA,gBAEF3C,OAAA;cAAA2C,QAAA,gBACE3C,OAAA;gBACE4C,KAAK,EAAE;kBACLY,KAAK,EAAE,uBAAuB;kBAC9BF,QAAQ,EAAE,UAAU;kBACpBD,YAAY,EAAE;gBAChB,CAAE;gBAAAV,QAAA,EACH;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJpD,OAAA;gBACE4C,KAAK,EAAE;kBACLU,QAAQ,EAAE,MAAM;kBAChBC,UAAU,EAAE,KAAK;kBACjBC,KAAK,EAAE,sBAAsB;kBAC7BI,MAAM,EAAE;gBACV,CAAE;gBAAAjB,QAAA,EAEDvC,KAAK,CAACI;cAAU;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNpD,OAAA;cAAK4C,KAAK,EAAE;gBAAEU,QAAQ,EAAE,MAAM;gBAAEO,OAAO,EAAE;cAAI,CAAE;cAAAlB,QAAA,EAAC;YAAE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpD,OAAA;QAAK0C,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB3C,OAAA;UAAK0C,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB3C,OAAA;YACE4C,KAAK,EAAE;cACLC,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE;YAClB,CAAE;YAAAH,QAAA,gBAEF3C,OAAA;cAAA2C,QAAA,gBACE3C,OAAA;gBACE4C,KAAK,EAAE;kBACLY,KAAK,EAAE,uBAAuB;kBAC9BF,QAAQ,EAAE,UAAU;kBACpBD,YAAY,EAAE;gBAChB,CAAE;gBAAAV,QAAA,EACH;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJpD,OAAA;gBACE4C,KAAK,EAAE;kBACLU,QAAQ,EAAE,MAAM;kBAChBC,UAAU,EAAE,KAAK;kBACjBC,KAAK,EAAE,sBAAsB;kBAC7BI,MAAM,EAAE;gBACV,CAAE;gBAAAjB,QAAA,EAEDvC,KAAK,CAACK;cAAU;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNpD,OAAA;cAAK4C,KAAK,EAAE;gBAAEU,QAAQ,EAAE,MAAM;gBAAEO,OAAO,EAAE;cAAI,CAAE;cAAAlB,QAAA,EAAC;YAAE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpD,OAAA;MACE4C,KAAK,EAAE;QACLC,OAAO,EAAE,MAAM;QACfa,mBAAmB,EAAE,sCAAsC;QAC3DC,GAAG,EAAE,MAAM;QACXN,YAAY,EAAE;MAChB,CAAE;MAAAV,QAAA,gBAGF3C,OAAA;QAAK0C,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB3C,OAAA;UAAK0C,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1B3C,OAAA;YAAI0C,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAa;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACNpD,OAAA;UAAK0C,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB3C,OAAA;YAAK4C,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEc,GAAG,EAAE;YAAO,CAAE;YAAAhB,QAAA,GAC1C,CAAC,CAAAxC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,IAAI,MAAK,YAAY,IAAI,CAAAxB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,IAAI,MAAK,OAAO,kBACrD3B,OAAA;cACE0C,SAAS,EAAC,iBAAiB;cAC3BoB,OAAO,EAAEA,CAAA,KAAOC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAU;cACjDrB,KAAK,EAAE;gBAAEE,cAAc,EAAE;cAAa,CAAE;cAAAH,QAAA,gBAExC3C,OAAA;gBAAM4C,KAAK,EAAE;kBAAEsB,WAAW,EAAE;gBAAU,CAAE;gBAAAvB,QAAA,EAAC;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,gBAEpD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT,eACDpD,OAAA;cACE0C,SAAS,EAAC,mBAAmB;cAC7BoB,OAAO,EAAEA,CAAA,KAAOC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,YAAc;cACrDrB,KAAK,EAAE;gBAAEE,cAAc,EAAE;cAAa,CAAE;cAAAH,QAAA,gBAExC3C,OAAA;gBAAM4C,KAAK,EAAE;kBAAEsB,WAAW,EAAE;gBAAU,CAAE;gBAAAvB,QAAA,EAAC;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,kBAEpD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpD,OAAA;cACE0C,SAAS,EAAC,mBAAmB;cAC7BoB,OAAO,EAAEA,CAAA,KAAOC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,UAAY;cACnDrB,KAAK,EAAE;gBAAEE,cAAc,EAAE;cAAa,CAAE;cAAAH,QAAA,gBAExC3C,OAAA;gBAAM4C,KAAK,EAAE;kBAAEsB,WAAW,EAAE;gBAAU,CAAE;gBAAAvB,QAAA,EAAC;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,oBAEpD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpD,OAAA;cACE0C,SAAS,EAAC,mBAAmB;cAC7BoB,OAAO,EAAEA,CAAA,KAAOC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,UAAY;cACnDrB,KAAK,EAAE;gBAAEE,cAAc,EAAE;cAAa,CAAE;cAAAH,QAAA,gBAExC3C,OAAA;gBAAM4C,KAAK,EAAE;kBAAEsB,WAAW,EAAE;gBAAU,CAAE;gBAAAvB,QAAA,EAAC;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,kBAEpD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpD,OAAA;QAAK0C,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB3C,OAAA;UAAK0C,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1B3C,OAAA;YAAI0C,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAa;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACNpD,OAAA;UAAK0C,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB3C,OAAA;YAAK4C,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEc,GAAG,EAAE;YAAO,CAAE;YAAAhB,QAAA,gBAC3C3C,OAAA;cACE4C,KAAK,EAAE;gBACLC,OAAO,EAAE,MAAM;gBACfC,cAAc,EAAE,eAAe;gBAC/BC,UAAU,EAAE,QAAQ;gBACpBoB,OAAO,EAAE,SAAS;gBAClBC,UAAU,EAAE,yBAAyB;gBACrCC,YAAY,EAAE;cAChB,CAAE;cAAA1B,QAAA,gBAEF3C,OAAA;gBACE4C,KAAK,EAAE;kBACLC,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBY,GAAG,EAAE;gBACP,CAAE;gBAAAhB,QAAA,gBAEF3C,OAAA;kBAAM4C,KAAK,EAAE;oBAAEU,QAAQ,EAAE;kBAAU,CAAE;kBAAAX,QAAA,EAAC;gBAAE;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/CpD,OAAA;kBAAM4C,KAAK,EAAE;oBAAEW,UAAU,EAAE;kBAAM,CAAE;kBAAAZ,QAAA,EAAC;gBAAa;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACNpD,OAAA;gBACE4C,KAAK,EAAE;kBAAEY,KAAK,EAAE,sBAAsB;kBAAED,UAAU,EAAE;gBAAM,CAAE;gBAAAZ,QAAA,EAC7D;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNpD,OAAA;cACE4C,KAAK,EAAE;gBACLC,OAAO,EAAE,MAAM;gBACfC,cAAc,EAAE,eAAe;gBAC/BC,UAAU,EAAE,QAAQ;gBACpBoB,OAAO,EAAE,SAAS;gBAClBC,UAAU,EAAE,yBAAyB;gBACrCC,YAAY,EAAE;cAChB,CAAE;cAAA1B,QAAA,gBAEF3C,OAAA;gBACE4C,KAAK,EAAE;kBACLC,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBY,GAAG,EAAE;gBACP,CAAE;gBAAAhB,QAAA,gBAEF3C,OAAA;kBAAM4C,KAAK,EAAE;oBAAEU,QAAQ,EAAE;kBAAU,CAAE;kBAAAX,QAAA,EAAC;gBAAE;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/CpD,OAAA;kBAAM4C,KAAK,EAAE;oBAAEW,UAAU,EAAE;kBAAM,CAAE;kBAAAZ,QAAA,EAAC;gBAAQ;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACNpD,OAAA;gBACE4C,KAAK,EAAE;kBAAEY,KAAK,EAAE,sBAAsB;kBAAED,UAAU,EAAE;gBAAM,CAAE;gBAAAZ,QAAA,EAC7D;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNpD,OAAA;cACE4C,KAAK,EAAE;gBACLC,OAAO,EAAE,MAAM;gBACfC,cAAc,EAAE,eAAe;gBAC/BC,UAAU,EAAE,QAAQ;gBACpBoB,OAAO,EAAE,SAAS;gBAClBC,UAAU,EAAE,yBAAyB;gBACrCC,YAAY,EAAE;cAChB,CAAE;cAAA1B,QAAA,gBAEF3C,OAAA;gBACE4C,KAAK,EAAE;kBACLC,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBY,GAAG,EAAE;gBACP,CAAE;gBAAAhB,QAAA,gBAEF3C,OAAA;kBAAM4C,KAAK,EAAE;oBAAEU,QAAQ,EAAE;kBAAU,CAAE;kBAAAX,QAAA,EAAC;gBAAE;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/CpD,OAAA;kBAAM4C,KAAK,EAAE;oBAAEW,UAAU,EAAE;kBAAM,CAAE;kBAAAZ,QAAA,EAAC;gBAAW;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,eACNpD,OAAA;gBACE4C,KAAK,EAAE;kBAAEY,KAAK,EAAE,uBAAuB;kBAAED,UAAU,EAAE;gBAAM,CAAE;gBAAAZ,QAAA,EAC9D;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNpD,OAAA;cACE4C,KAAK,EAAE;gBACLC,OAAO,EAAE,MAAM;gBACfC,cAAc,EAAE,eAAe;gBAC/BC,UAAU,EAAE,QAAQ;gBACpBoB,OAAO,EAAE,SAAS;gBAClBC,UAAU,EAAE,yBAAyB;gBACrCC,YAAY,EAAE;cAChB,CAAE;cAAA1B,QAAA,gBAEF3C,OAAA;gBACE4C,KAAK,EAAE;kBACLC,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBY,GAAG,EAAE;gBACP,CAAE;gBAAAhB,QAAA,gBAEF3C,OAAA;kBAAM4C,KAAK,EAAE;oBAAEU,QAAQ,EAAE;kBAAU,CAAE;kBAAAX,QAAA,EAAC;gBAAC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9CpD,OAAA;kBAAM4C,KAAK,EAAE;oBAAEW,UAAU,EAAE;kBAAM,CAAE;kBAAAZ,QAAA,EAAC;gBAAW;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,eACNpD,OAAA;gBACE4C,KAAK,EAAE;kBAAEY,KAAK,EAAE,sBAAsB;kBAAED,UAAU,EAAE;gBAAM,CAAE;gBAAAZ,QAAA,EAC7D;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL,CAAC,CAAAjD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,IAAI,MAAK,YAAY,IAAI,CAAAxB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,IAAI,MAAK,OAAO,kBACrD3B,OAAA;MAAK0C,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB3C,OAAA;QAAK0C,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B3C,OAAA;UAAI0C,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAY;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5CpD,OAAA;UACE0C,SAAS,EAAC,wBAAwB;UAClCoB,OAAO,EAAEA,CAAA,KAAOC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAU;UAAAtB,QAAA,EAClD;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNpD,OAAA;QAAK0C,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBjC,WAAW,CAACgB,MAAM,GAAG,CAAC,gBACrB1B,OAAA;UAAK4C,KAAK,EAAE;YAAE0B,SAAS,EAAE;UAAO,CAAE;UAAA3B,QAAA,eAChC3C,OAAA;YAAO4C,KAAK,EAAE;cAAE2B,KAAK,EAAE,MAAM;cAAEC,cAAc,EAAE;YAAW,CAAE;YAAA7B,QAAA,gBAC1D3C,OAAA;cAAA2C,QAAA,eACE3C,OAAA;gBACE4C,KAAK,EAAE;kBAAE6B,YAAY,EAAE;gBAAgC,CAAE;gBAAA9B,QAAA,gBAEzD3C,OAAA;kBACE4C,KAAK,EAAE;oBACLuB,OAAO,EAAE,MAAM;oBACfO,SAAS,EAAE,MAAM;oBACjBlB,KAAK,EAAE,uBAAuB;oBAC9BD,UAAU,EAAE;kBACd,CAAE;kBAAAZ,QAAA,EACH;gBAED;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBACE4C,KAAK,EAAE;oBACLuB,OAAO,EAAE,MAAM;oBACfO,SAAS,EAAE,MAAM;oBACjBlB,KAAK,EAAE,uBAAuB;oBAC9BD,UAAU,EAAE;kBACd,CAAE;kBAAAZ,QAAA,EACH;gBAED;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBACE4C,KAAK,EAAE;oBACLuB,OAAO,EAAE,MAAM;oBACfO,SAAS,EAAE,MAAM;oBACjBlB,KAAK,EAAE,uBAAuB;oBAC9BD,UAAU,EAAE;kBACd,CAAE;kBAAAZ,QAAA,EACH;gBAED;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpD,OAAA;kBACE4C,KAAK,EAAE;oBACLuB,OAAO,EAAE,MAAM;oBACfO,SAAS,EAAE,MAAM;oBACjBlB,KAAK,EAAE,uBAAuB;oBAC9BD,UAAU,EAAE;kBACd,CAAE;kBAAAZ,QAAA,EACH;gBAED;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRpD,OAAA;cAAA2C,QAAA,EACGjC,WAAW,CAACiE,GAAG,CAAExE,IAAI,iBACpBH,OAAA;gBAEE4C,KAAK,EAAE;kBACL6B,YAAY,EAAE;gBAChB,CAAE;gBAAA9B,QAAA,gBAEF3C,OAAA;kBAAI4C,KAAK,EAAE;oBAAEuB,OAAO,EAAE;kBAAO,CAAE;kBAAAxB,QAAA,eAC7B3C,OAAA;oBAAA2C,QAAA,gBACE3C,OAAA;sBACE4C,KAAK,EAAE;wBACLW,UAAU,EAAE,KAAK;wBACjBC,KAAK,EAAE;sBACT,CAAE;sBAAAb,QAAA,EAEDxC,IAAI,CAACsD;oBAAI;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC,eACNpD,OAAA;sBACE4C,KAAK,EAAE;wBACLU,QAAQ,EAAE,UAAU;wBACpBE,KAAK,EAAE;sBACT,CAAE;sBAAAb,QAAA,EAEDxC,IAAI,CAACyE;oBAAK;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACLpD,OAAA;kBAAI4C,KAAK,EAAE;oBAAEuB,OAAO,EAAE;kBAAO,CAAE;kBAAAxB,QAAA,eAC7B3C,OAAA;oBACE4C,KAAK,EAAE;sBACLuB,OAAO,EAAE,iBAAiB;sBAC1BE,YAAY,EAAE,QAAQ;sBACtBf,QAAQ,EAAE,SAAS;sBACnBC,UAAU,EAAE,KAAK;sBACjBsB,eAAe,EAAEpC,YAAY,CAACtC,IAAI,CAACwB,IAAI,CAAC,GAAG,IAAI;sBAC/C6B,KAAK,EAAEf,YAAY,CAACtC,IAAI,CAACwB,IAAI;oBAC/B,CAAE;oBAAAgB,QAAA,EAEDxC,IAAI,CAACwB;kBAAI;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACLpD,OAAA;kBAAI4C,KAAK,EAAE;oBAAEuB,OAAO,EAAE;kBAAO,CAAE;kBAAAxB,QAAA,eAC7B3C,OAAA;oBACE4C,KAAK,EAAE;sBACLuB,OAAO,EAAE,iBAAiB;sBAC1BE,YAAY,EAAE,QAAQ;sBACtBf,QAAQ,EAAE,SAAS;sBACnBC,UAAU,EAAE,KAAK;sBACjBsB,eAAe,EAAE1E,IAAI,CAACsB,QAAQ,GAC1B,wBAAwB,GACxB,sBAAsB;sBAC1B+B,KAAK,EAAErD,IAAI,CAACsB,QAAQ,GAChB,sBAAsB,GACtB;oBACN,CAAE;oBAAAkB,QAAA,EAEDxC,IAAI,CAACsB,QAAQ,GAAG,QAAQ,GAAG;kBAAU;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACLpD,OAAA;kBACE4C,KAAK,EAAE;oBACLuB,OAAO,EAAE,MAAM;oBACfX,KAAK,EAAE;kBACT,CAAE;kBAAAb,QAAA,EAEDZ,UAAU,CAAC5B,IAAI,CAAC2E,SAAS;gBAAC;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA,GAhEAjD,IAAI,CAAC4E,GAAG;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiEX,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,gBAENpD,OAAA;UACE4C,KAAK,EAAE;YACL8B,SAAS,EAAE,QAAQ;YACnBP,OAAO,EAAE,MAAM;YACfX,KAAK,EAAE;UACT,CAAE;UAAAb,QAAA,EACH;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAClD,EAAA,CAxkBID,SAAS;EAAA,QACIJ,OAAO;AAAA;AAAAmF,EAAA,GADpB/E,SAAS;AA0kBf,eAAeA,SAAS;AAAC,IAAA+E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}