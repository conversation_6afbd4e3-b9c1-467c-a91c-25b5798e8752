{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/components/Header.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useAuth } from \"../context/AuthContext\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = ({\n  onToggleSidebar,\n  sidebarCollapsed\n}) => {\n  _s();\n  var _user$name, _user$name$charAt;\n  const {\n    user,\n    logout\n  } = useAuth();\n  const navigate = useNavigate();\n  const [showUserMenu, setShowUserMenu] = useState(false);\n  const handleLogout = () => {\n    logout();\n    navigate(\"/login\");\n  };\n  const handleProfile = () => {\n    navigate(\"/profile\");\n    setShowUserMenu(false);\n  };\n  const handleSettings = () => {\n    navigate(\"/settings\");\n    setShowUserMenu(false);\n  };\n  const getRoleColor = role => {\n    switch (role) {\n      case \"superadmin\":\n        return \"#ef4444\";\n      case \"admin\":\n        return \"#3b82f6\";\n      case \"agent\":\n        return \"#10b981\";\n      default:\n        return \"#64748b\";\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: `header ${sidebarCollapsed ? \"sidebar-collapsed\" : \"\"}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-left\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"sidebar-toggle-btn\",\n          onClick: onToggleSidebar,\n          \"aria-label\": \"Toggle sidebar\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"20\",\n            height: \"20\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            children: [/*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"3\",\n              y1: \"6\",\n              x2: \"21\",\n              y2: \"6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"3\",\n              y1: \"12\",\n              x2: \"21\",\n              y2: \"12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"3\",\n              y1: \"18\",\n              x2: \"21\",\n              y2: \"18\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-logo\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"logo-icon\",\n            children: \"AP\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"logo-text\",\n            children: \"Admin Panel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-user\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-profile\",\n          onClick: () => setShowUserMenu(!showUserMenu),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-avatar\",\n            children: user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : (_user$name$charAt = _user$name.charAt(0)) === null || _user$name$charAt === void 0 ? void 0 : _user$name$charAt.toUpperCase()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"user-name\",\n              children: user === null || user === void 0 ? void 0 : user.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"user-role\",\n              style: {\n                color: getRoleColor(user === null || user === void 0 ? void 0 : user.role)\n              },\n              children: user === null || user === void 0 ? void 0 : user.role\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"dropdown-arrow\",\n            children: \"\\u25BC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this), showUserMenu && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-dropdown\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"dropdown-item\",\n            onClick: handleProfile,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"dropdown-icon\",\n              children: \"\\uD83D\\uDC64\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 17\n            }, this), \"Profile\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"dropdown-item\",\n            onClick: handleSettings,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"dropdown-icon\",\n              children: \"\\u2699\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 17\n            }, this), \"Settings\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"dropdown-divider\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"dropdown-item logout\",\n            onClick: handleLogout,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"dropdown-icon\",\n              children: \"\\uD83D\\uDEAA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 17\n            }, this), \"Logout\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this), showUserMenu && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dropdown-overlay\",\n      onClick: () => setShowUserMenu(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"l/dNgIzDD5aHvjW6n1QIsKhZ5BI=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useAuth", "jsxDEV", "_jsxDEV", "Header", "onToggleSidebar", "sidebarCollapsed", "_s", "_user$name", "_user$name$charAt", "user", "logout", "navigate", "showUserMenu", "setShowUserMenu", "handleLogout", "handleProfile", "handleSettings", "getRoleColor", "role", "className", "children", "onClick", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "x1", "y1", "x2", "y2", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "char<PERSON>t", "toUpperCase", "style", "color", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/components/Header.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useAuth } from \"../context/AuthContext\";\n\nconst Header = ({ onToggleSidebar, sidebarCollapsed }) => {\n  const { user, logout } = useAuth();\n  const navigate = useNavigate();\n  const [showUserMenu, setShowUserMenu] = useState(false);\n\n  const handleLogout = () => {\n    logout();\n    navigate(\"/login\");\n  };\n\n  const handleProfile = () => {\n    navigate(\"/profile\");\n    setShowUserMenu(false);\n  };\n\n  const handleSettings = () => {\n    navigate(\"/settings\");\n    setShowUserMenu(false);\n  };\n\n  const getRoleColor = (role) => {\n    switch (role) {\n      case \"superadmin\":\n        return \"#ef4444\";\n      case \"admin\":\n        return \"#3b82f6\";\n      case \"agent\":\n        return \"#10b981\";\n      default:\n        return \"#64748b\";\n    }\n  };\n\n  return (\n    <header className={`header ${sidebarCollapsed ? \"sidebar-collapsed\" : \"\"}`}>\n      <div className=\"header-container\">\n        {/* Sidebar Toggle & Logo */}\n        <div className=\"header-left\">\n          <button\n            className=\"sidebar-toggle-btn\"\n            onClick={onToggleSidebar}\n            aria-label=\"Toggle sidebar\"\n          >\n            <svg\n              width=\"20\"\n              height=\"20\"\n              viewBox=\"0 0 24 24\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              strokeWidth=\"2\"\n            >\n              <line x1=\"3\" y1=\"6\" x2=\"21\" y2=\"6\"></line>\n              <line x1=\"3\" y1=\"12\" x2=\"21\" y2=\"12\"></line>\n              <line x1=\"3\" y1=\"18\" x2=\"21\" y2=\"18\"></line>\n            </svg>\n          </button>\n          <div className=\"header-logo\">\n            <div className=\"logo-icon\">AP</div>\n            <span className=\"logo-text\">Admin Panel</span>\n          </div>\n        </div>\n\n        {/* User Menu */}\n        <div className=\"header-user\">\n          <div\n            className=\"user-profile\"\n            onClick={() => setShowUserMenu(!showUserMenu)}\n          >\n            <div className=\"user-avatar\">\n              {user?.name?.charAt(0)?.toUpperCase()}\n            </div>\n            <div className=\"user-info\">\n              <div className=\"user-name\">{user?.name}</div>\n              <div\n                className=\"user-role\"\n                style={{ color: getRoleColor(user?.role) }}\n              >\n                {user?.role}\n              </div>\n            </div>\n            <div className=\"dropdown-arrow\">▼</div>\n          </div>\n\n          {/* Dropdown Menu */}\n          {showUserMenu && (\n            <div className=\"user-dropdown\">\n              <button className=\"dropdown-item\" onClick={handleProfile}>\n                <span className=\"dropdown-icon\">👤</span>\n                Profile\n              </button>\n              <button className=\"dropdown-item\" onClick={handleSettings}>\n                <span className=\"dropdown-icon\">⚙️</span>\n                Settings\n              </button>\n              <div className=\"dropdown-divider\"></div>\n              <button className=\"dropdown-item logout\" onClick={handleLogout}>\n                <span className=\"dropdown-icon\">🚪</span>\n                Logout\n              </button>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Click outside to close dropdown */}\n      {showUserMenu && (\n        <div\n          className=\"dropdown-overlay\"\n          onClick={() => setShowUserMenu(false)}\n        ></div>\n      )}\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,MAAM,GAAGA,CAAC;EAAEC,eAAe;EAAEC;AAAiB,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,UAAA,EAAAC,iBAAA;EACxD,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGV,OAAO,CAAC,CAAC;EAClC,MAAMW,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACa,YAAY,EAAEC,eAAe,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMgB,YAAY,GAAGA,CAAA,KAAM;IACzBJ,MAAM,CAAC,CAAC;IACRC,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMI,aAAa,GAAGA,CAAA,KAAM;IAC1BJ,QAAQ,CAAC,UAAU,CAAC;IACpBE,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMG,cAAc,GAAGA,CAAA,KAAM;IAC3BL,QAAQ,CAAC,WAAW,CAAC;IACrBE,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMI,YAAY,GAAIC,IAAI,IAAK;IAC7B,QAAQA,IAAI;MACV,KAAK,YAAY;QACf,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,oBACEhB,OAAA;IAAQiB,SAAS,EAAE,UAAUd,gBAAgB,GAAG,mBAAmB,GAAG,EAAE,EAAG;IAAAe,QAAA,gBACzElB,OAAA;MAAKiB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAE/BlB,OAAA;QAAKiB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlB,OAAA;UACEiB,SAAS,EAAC,oBAAoB;UAC9BE,OAAO,EAAEjB,eAAgB;UACzB,cAAW,gBAAgB;UAAAgB,QAAA,eAE3BlB,OAAA;YACEoB,KAAK,EAAC,IAAI;YACVC,MAAM,EAAC,IAAI;YACXC,OAAO,EAAC,WAAW;YACnBC,IAAI,EAAC,MAAM;YACXC,MAAM,EAAC,cAAc;YACrBC,WAAW,EAAC,GAAG;YAAAP,QAAA,gBAEflB,OAAA;cAAM0B,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1CjC,OAAA;cAAM0B,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5CjC,OAAA;cAAM0B,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACTjC,OAAA;UAAKiB,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BlB,OAAA;YAAKiB,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAE;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnCjC,OAAA;YAAMiB,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAW;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjC,OAAA;QAAKiB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlB,OAAA;UACEiB,SAAS,EAAC,cAAc;UACxBE,OAAO,EAAEA,CAAA,KAAMR,eAAe,CAAC,CAACD,YAAY,CAAE;UAAAQ,QAAA,gBAE9ClB,OAAA;YAAKiB,SAAS,EAAC,aAAa;YAAAC,QAAA,EACzBX,IAAI,aAAJA,IAAI,wBAAAF,UAAA,GAAJE,IAAI,CAAE2B,IAAI,cAAA7B,UAAA,wBAAAC,iBAAA,GAAVD,UAAA,CAAY8B,MAAM,CAAC,CAAC,CAAC,cAAA7B,iBAAA,uBAArBA,iBAAA,CAAuB8B,WAAW,CAAC;UAAC;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACNjC,OAAA;YAAKiB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBlB,OAAA;cAAKiB,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAEX,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2B;YAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7CjC,OAAA;cACEiB,SAAS,EAAC,WAAW;cACrBoB,KAAK,EAAE;gBAAEC,KAAK,EAAEvB,YAAY,CAACR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,IAAI;cAAE,CAAE;cAAAE,QAAA,EAE1CX,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES;YAAI;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNjC,OAAA;YAAKiB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAC;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,EAGLvB,YAAY,iBACXV,OAAA;UAAKiB,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BlB,OAAA;YAAQiB,SAAS,EAAC,eAAe;YAACE,OAAO,EAAEN,aAAc;YAAAK,QAAA,gBACvDlB,OAAA;cAAMiB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,WAE3C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjC,OAAA;YAAQiB,SAAS,EAAC,eAAe;YAACE,OAAO,EAAEL,cAAe;YAAAI,QAAA,gBACxDlB,OAAA;cAAMiB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,YAE3C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjC,OAAA;YAAKiB,SAAS,EAAC;UAAkB;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxCjC,OAAA;YAAQiB,SAAS,EAAC,sBAAsB;YAACE,OAAO,EAAEP,YAAa;YAAAM,QAAA,gBAC7DlB,OAAA;cAAMiB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,UAE3C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLvB,YAAY,iBACXV,OAAA;MACEiB,SAAS,EAAC,kBAAkB;MAC5BE,OAAO,EAAEA,CAAA,KAAMR,eAAe,CAAC,KAAK;IAAE;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAAC7B,EAAA,CAjHIH,MAAM;EAAA,QACeH,OAAO,EACfD,WAAW;AAAA;AAAA0C,EAAA,GAFxBtC,MAAM;AAmHZ,eAAeA,MAAM;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}