{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/components/Sidebar.jsx\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport { NavLink } from \"react-router-dom\";\nimport { useAuth } from \"../context/AuthContext\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Sidebar = ({\n  isCollapsed,\n  isOpen,\n  onClose\n}) => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const menuItems = [{\n    path: \"/dashboard\",\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      width: \"20\",\n      height: \"20\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      strokeWidth: \"2\",\n      children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n        x: \"3\",\n        y: \"3\",\n        width: \"7\",\n        height: \"7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n        x: \"14\",\n        y: \"3\",\n        width: \"7\",\n        height: \"7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n        x: \"14\",\n        y: \"14\",\n        width: \"7\",\n        height: \"7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n        x: \"3\",\n        y: \"14\",\n        width: \"7\",\n        height: \"7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 9\n    }, this),\n    label: \"Dashboard\",\n    roles: [\"superadmin\", \"admin\", \"agent\"]\n  }, {\n    path: \"/users\",\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      width: \"20\",\n      height: \"20\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      strokeWidth: \"2\",\n      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n        cx: \"9\",\n        cy: \"7\",\n        r: \"4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M23 21v-2a4 4 0 0 0-3-3.87\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M16 3.13a4 4 0 0 1 0 7.75\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 9\n    }, this),\n    label: \"User Management\",\n    roles: [\"superadmin\", \"admin\"]\n  }, {\n    path: \"/analytics\",\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      width: \"20\",\n      height: \"20\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      strokeWidth: \"2\",\n      children: /*#__PURE__*/_jsxDEV(\"polyline\", {\n        points: \"22,12 18,12 15,21 9,3 6,12 2,12\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 9\n    }, this),\n    label: \"Analytics\",\n    roles: [\"superadmin\", \"admin\", \"agent\"]\n  }, {\n    path: \"/reports\",\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      width: \"20\",\n      height: \"20\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      strokeWidth: \"2\",\n      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n        points: \"14,2 14,8 20,8\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n        x1: \"16\",\n        y1: \"13\",\n        x2: \"8\",\n        y2: \"13\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n        x1: \"16\",\n        y1: \"17\",\n        x2: \"8\",\n        y2: \"17\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n        points: \"10,9 9,9 8,9\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 9\n    }, this),\n    label: \"Reports\",\n    roles: [\"superadmin\", \"admin\"]\n  }, {\n    path: \"/settings\",\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      width: \"20\",\n      height: \"20\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      strokeWidth: \"2\",\n      children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n        cx: \"12\",\n        cy: \"12\",\n        r: \"3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 9\n    }, this),\n    label: \"Settings\",\n    roles: [\"superadmin\", \"admin\"]\n  }];\n\n  // Filter menu items based on user role\n  const filteredMenuItems = menuItems.filter(item => item.roles.includes(user === null || user === void 0 ? void 0 : user.role));\n  return /*#__PURE__*/_jsxDEV(\"aside\", {\n    className: `sidebar ${isCollapsed ? \"collapsed\" : \"\"} ${isOpen ? \"open\" : \"\"}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-brand\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"brand-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"24\",\n            height: \"24\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            children: /*#__PURE__*/_jsxDEV(\"polygon\", {\n              points: \"13,2 3,14 12,14 11,22 21,10 12,10 13,2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"sidebar-nav\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-section\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"nav-list\",\n          children: filteredMenuItems.map(item => /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(NavLink, {\n              to: item.path,\n              className: ({\n                isActive\n              }) => `nav-link ${isActive ? \"active\" : \"\"}`,\n              title: isCollapsed ? item.label : \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"nav-icon\",\n                children: item.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 19\n              }, this), !isCollapsed && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"nav-label\",\n                children: item.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 21\n              }, this), !isCollapsed && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"nav-indicator\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 36\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 17\n            }, this)\n          }, item.path, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar-footer\",\n      children: [!isCollapsed && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-avatar\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"20\",\n            height: \"20\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            children: [/*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n              cx: \"12\",\n              cy: \"7\",\n              r: \"4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-name\",\n            children: user === null || user === void 0 ? void 0 : user.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-role\",\n            children: user === null || user === void 0 ? void 0 : user.role\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-actions\",\n        children: /*#__PURE__*/_jsxDEV(NavLink, {\n          to: \"/profile\",\n          className: \"footer-action\",\n          title: \"Profile\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"18\",\n            height: \"18\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            children: [/*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n              cx: \"12\",\n              cy: \"7\",\n              r: \"4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), !isCollapsed && /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 30\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 113,\n    columnNumber: 5\n  }, this);\n};\n_s(Sidebar, \"9ep4vdl3mBfipxjmc+tQCDhw6Ik=\", false, function () {\n  return [useAuth];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "NavLink", "useAuth", "jsxDEV", "_jsxDEV", "Sidebar", "isCollapsed", "isOpen", "onClose", "_s", "user", "menuItems", "path", "icon", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "children", "x", "y", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "roles", "d", "cx", "cy", "r", "points", "x1", "y1", "x2", "y2", "filteredMenuItems", "filter", "item", "includes", "role", "className", "map", "to", "isActive", "title", "name", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/components/Sidebar.jsx"], "sourcesContent": ["import React from \"react\";\nimport { NavLink } from \"react-router-dom\";\nimport { useAuth } from \"../context/AuthContext\";\n\nconst Sidebar = ({ isCollapsed, isOpen, onClose }) => {\n  const { user } = useAuth();\n\n  const menuItems = [\n    {\n      path: \"/dashboard\",\n      icon: (\n        <svg\n          width=\"20\"\n          height=\"20\"\n          viewBox=\"0 0 24 24\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          strokeWidth=\"2\"\n        >\n          <rect x=\"3\" y=\"3\" width=\"7\" height=\"7\"></rect>\n          <rect x=\"14\" y=\"3\" width=\"7\" height=\"7\"></rect>\n          <rect x=\"14\" y=\"14\" width=\"7\" height=\"7\"></rect>\n          <rect x=\"3\" y=\"14\" width=\"7\" height=\"7\"></rect>\n        </svg>\n      ),\n      label: \"Dashboard\",\n      roles: [\"superadmin\", \"admin\", \"agent\"],\n    },\n    {\n      path: \"/users\",\n      icon: (\n        <svg\n          width=\"20\"\n          height=\"20\"\n          viewBox=\"0 0 24 24\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          strokeWidth=\"2\"\n        >\n          <path d=\"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\"></path>\n          <circle cx=\"9\" cy=\"7\" r=\"4\"></circle>\n          <path d=\"M23 21v-2a4 4 0 0 0-3-3.87\"></path>\n          <path d=\"M16 3.13a4 4 0 0 1 0 7.75\"></path>\n        </svg>\n      ),\n      label: \"User Management\",\n      roles: [\"superadmin\", \"admin\"],\n    },\n    {\n      path: \"/analytics\",\n      icon: (\n        <svg\n          width=\"20\"\n          height=\"20\"\n          viewBox=\"0 0 24 24\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          strokeWidth=\"2\"\n        >\n          <polyline points=\"22,12 18,12 15,21 9,3 6,12 2,12\"></polyline>\n        </svg>\n      ),\n      label: \"Analytics\",\n      roles: [\"superadmin\", \"admin\", \"agent\"],\n    },\n    {\n      path: \"/reports\",\n      icon: (\n        <svg\n          width=\"20\"\n          height=\"20\"\n          viewBox=\"0 0 24 24\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          strokeWidth=\"2\"\n        >\n          <path d=\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\"></path>\n          <polyline points=\"14,2 14,8 20,8\"></polyline>\n          <line x1=\"16\" y1=\"13\" x2=\"8\" y2=\"13\"></line>\n          <line x1=\"16\" y1=\"17\" x2=\"8\" y2=\"17\"></line>\n          <polyline points=\"10,9 9,9 8,9\"></polyline>\n        </svg>\n      ),\n      label: \"Reports\",\n      roles: [\"superadmin\", \"admin\"],\n    },\n    {\n      path: \"/settings\",\n      icon: (\n        <svg\n          width=\"20\"\n          height=\"20\"\n          viewBox=\"0 0 24 24\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          strokeWidth=\"2\"\n        >\n          <circle cx=\"12\" cy=\"12\" r=\"3\"></circle>\n          <path d=\"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z\"></path>\n        </svg>\n      ),\n      label: \"Settings\",\n      roles: [\"superadmin\", \"admin\"],\n    },\n  ];\n\n  // Filter menu items based on user role\n  const filteredMenuItems = menuItems.filter((item) =>\n    item.roles.includes(user?.role)\n  );\n\n  return (\n    <aside\n      className={`sidebar ${isCollapsed ? \"collapsed\" : \"\"} ${\n        isOpen ? \"open\" : \"\"\n      }`}\n    >\n      {/* Sidebar Header */}\n      <div className=\"sidebar-header\">\n        <div className=\"sidebar-brand\">\n          <div className=\"brand-icon\">\n            <svg\n              width=\"24\"\n              height=\"24\"\n              viewBox=\"0 0 24 24\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              strokeWidth=\"2\"\n            >\n              <polygon points=\"13,2 3,14 12,14 11,22 21,10 12,10 13,2\"></polygon>\n            </svg>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation Menu */}\n      <nav className=\"sidebar-nav\">\n        <div className=\"nav-section\">\n          <ul className=\"nav-list\">\n            {filteredMenuItems.map((item) => (\n              <li key={item.path} className=\"nav-item\">\n                <NavLink\n                  to={item.path}\n                  className={({ isActive }) =>\n                    `nav-link ${isActive ? \"active\" : \"\"}`\n                  }\n                  title={isCollapsed ? item.label : \"\"}\n                >\n                  <div className=\"nav-icon\">{item.icon}</div>\n                  {!isCollapsed && (\n                    <span className=\"nav-label\">{item.label}</span>\n                  )}\n                  {!isCollapsed && <div className=\"nav-indicator\"></div>}\n                </NavLink>\n              </li>\n            ))}\n          </ul>\n        </div>\n      </nav>\n\n      {/* Sidebar Footer */}\n      <div className=\"sidebar-footer\">\n        {!isCollapsed && (\n          <div className=\"user-card\">\n            <div className=\"user-avatar\">\n              <svg\n                width=\"20\"\n                height=\"20\"\n                viewBox=\"0 0 24 24\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                strokeWidth=\"2\"\n              >\n                <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path>\n                <circle cx=\"12\" cy=\"7\" r=\"4\"></circle>\n              </svg>\n            </div>\n            <div className=\"user-info\">\n              <div className=\"user-name\">{user?.name}</div>\n              <div className=\"user-role\">{user?.role}</div>\n            </div>\n          </div>\n        )}\n\n        <div className=\"footer-actions\">\n          <NavLink to=\"/profile\" className=\"footer-action\" title=\"Profile\">\n            <svg\n              width=\"18\"\n              height=\"18\"\n              viewBox=\"0 0 24 24\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              strokeWidth=\"2\"\n            >\n              <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path>\n              <circle cx=\"12\" cy=\"7\" r=\"4\"></circle>\n            </svg>\n            {!isCollapsed && <span>Profile</span>}\n          </NavLink>\n        </div>\n      </div>\n    </aside>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,OAAO,GAAGA,CAAC;EAAEC,WAAW;EAAEC,MAAM;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACpD,MAAM;IAAEC;EAAK,CAAC,GAAGR,OAAO,CAAC,CAAC;EAE1B,MAAMS,SAAS,GAAG,CAChB;IACEC,IAAI,EAAE,YAAY;IAClBC,IAAI,eACFT,OAAA;MACEU,KAAK,EAAC,IAAI;MACVC,MAAM,EAAC,IAAI;MACXC,OAAO,EAAC,WAAW;MACnBC,IAAI,EAAC,MAAM;MACXC,MAAM,EAAC,cAAc;MACrBC,WAAW,EAAC,GAAG;MAAAC,QAAA,gBAEfhB,OAAA;QAAMiB,CAAC,EAAC,GAAG;QAACC,CAAC,EAAC,GAAG;QAACR,KAAK,EAAC,GAAG;QAACC,MAAM,EAAC;MAAG;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC9CtB,OAAA;QAAMiB,CAAC,EAAC,IAAI;QAACC,CAAC,EAAC,GAAG;QAACR,KAAK,EAAC,GAAG;QAACC,MAAM,EAAC;MAAG;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC/CtB,OAAA;QAAMiB,CAAC,EAAC,IAAI;QAACC,CAAC,EAAC,IAAI;QAACR,KAAK,EAAC,GAAG;QAACC,MAAM,EAAC;MAAG;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAChDtB,OAAA;QAAMiB,CAAC,EAAC,GAAG;QAACC,CAAC,EAAC,IAAI;QAACR,KAAK,EAAC,GAAG;QAACC,MAAM,EAAC;MAAG;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CACN;IACDC,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE,CAAC,YAAY,EAAE,OAAO,EAAE,OAAO;EACxC,CAAC,EACD;IACEhB,IAAI,EAAE,QAAQ;IACdC,IAAI,eACFT,OAAA;MACEU,KAAK,EAAC,IAAI;MACVC,MAAM,EAAC,IAAI;MACXC,OAAO,EAAC,WAAW;MACnBC,IAAI,EAAC,MAAM;MACXC,MAAM,EAAC,cAAc;MACrBC,WAAW,EAAC,GAAG;MAAAC,QAAA,gBAEfhB,OAAA;QAAMyB,CAAC,EAAC;MAA2C;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC3DtB,OAAA;QAAQ0B,EAAE,EAAC,GAAG;QAACC,EAAE,EAAC,GAAG;QAACC,CAAC,EAAC;MAAG;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,eACrCtB,OAAA;QAAMyB,CAAC,EAAC;MAA4B;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC5CtB,OAAA;QAAMyB,CAAC,EAAC;MAA2B;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CACN;IACDC,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,CAAC,YAAY,EAAE,OAAO;EAC/B,CAAC,EACD;IACEhB,IAAI,EAAE,YAAY;IAClBC,IAAI,eACFT,OAAA;MACEU,KAAK,EAAC,IAAI;MACVC,MAAM,EAAC,IAAI;MACXC,OAAO,EAAC,WAAW;MACnBC,IAAI,EAAC,MAAM;MACXC,MAAM,EAAC,cAAc;MACrBC,WAAW,EAAC,GAAG;MAAAC,QAAA,eAEfhB,OAAA;QAAU6B,MAAM,EAAC;MAAiC;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D,CACN;IACDC,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE,CAAC,YAAY,EAAE,OAAO,EAAE,OAAO;EACxC,CAAC,EACD;IACEhB,IAAI,EAAE,UAAU;IAChBC,IAAI,eACFT,OAAA;MACEU,KAAK,EAAC,IAAI;MACVC,MAAM,EAAC,IAAI;MACXC,OAAO,EAAC,WAAW;MACnBC,IAAI,EAAC,MAAM;MACXC,MAAM,EAAC,cAAc;MACrBC,WAAW,EAAC,GAAG;MAAAC,QAAA,gBAEfhB,OAAA;QAAMyB,CAAC,EAAC;MAA4D;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC5EtB,OAAA;QAAU6B,MAAM,EAAC;MAAgB;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eAC7CtB,OAAA;QAAM8B,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,GAAG;QAACC,EAAE,EAAC;MAAI;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC5CtB,OAAA;QAAM8B,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,GAAG;QAACC,EAAE,EAAC;MAAI;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC5CtB,OAAA;QAAU6B,MAAM,EAAC;MAAc;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CACN;IACDC,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,CAAC,YAAY,EAAE,OAAO;EAC/B,CAAC,EACD;IACEhB,IAAI,EAAE,WAAW;IACjBC,IAAI,eACFT,OAAA;MACEU,KAAK,EAAC,IAAI;MACVC,MAAM,EAAC,IAAI;MACXC,OAAO,EAAC,WAAW;MACnBC,IAAI,EAAC,MAAM;MACXC,MAAM,EAAC,cAAc;MACrBC,WAAW,EAAC,GAAG;MAAAC,QAAA,gBAEfhB,OAAA;QAAQ0B,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,CAAC,EAAC;MAAG;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,eACvCtB,OAAA;QAAMyB,CAAC,EAAC;MAAguB;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7uB,CACN;IACDC,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,CAAC,YAAY,EAAE,OAAO;EAC/B,CAAC,CACF;;EAED;EACA,MAAMU,iBAAiB,GAAG3B,SAAS,CAAC4B,MAAM,CAAEC,IAAI,IAC9CA,IAAI,CAACZ,KAAK,CAACa,QAAQ,CAAC/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgC,IAAI,CAChC,CAAC;EAED,oBACEtC,OAAA;IACEuC,SAAS,EAAE,WAAWrC,WAAW,GAAG,WAAW,GAAG,EAAE,IAClDC,MAAM,GAAG,MAAM,GAAG,EAAE,EACnB;IAAAa,QAAA,gBAGHhB,OAAA;MAAKuC,SAAS,EAAC,gBAAgB;MAAAvB,QAAA,eAC7BhB,OAAA;QAAKuC,SAAS,EAAC,eAAe;QAAAvB,QAAA,eAC5BhB,OAAA;UAAKuC,SAAS,EAAC,YAAY;UAAAvB,QAAA,eACzBhB,OAAA;YACEU,KAAK,EAAC,IAAI;YACVC,MAAM,EAAC,IAAI;YACXC,OAAO,EAAC,WAAW;YACnBC,IAAI,EAAC,MAAM;YACXC,MAAM,EAAC,cAAc;YACrBC,WAAW,EAAC,GAAG;YAAAC,QAAA,eAEfhB,OAAA;cAAS6B,MAAM,EAAC;YAAwC;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtB,OAAA;MAAKuC,SAAS,EAAC,aAAa;MAAAvB,QAAA,eAC1BhB,OAAA;QAAKuC,SAAS,EAAC,aAAa;QAAAvB,QAAA,eAC1BhB,OAAA;UAAIuC,SAAS,EAAC,UAAU;UAAAvB,QAAA,EACrBkB,iBAAiB,CAACM,GAAG,CAAEJ,IAAI,iBAC1BpC,OAAA;YAAoBuC,SAAS,EAAC,UAAU;YAAAvB,QAAA,eACtChB,OAAA,CAACH,OAAO;cACN4C,EAAE,EAAEL,IAAI,CAAC5B,IAAK;cACd+B,SAAS,EAAEA,CAAC;gBAAEG;cAAS,CAAC,KACtB,YAAYA,QAAQ,GAAG,QAAQ,GAAG,EAAE,EACrC;cACDC,KAAK,EAAEzC,WAAW,GAAGkC,IAAI,CAACb,KAAK,GAAG,EAAG;cAAAP,QAAA,gBAErChB,OAAA;gBAAKuC,SAAS,EAAC,UAAU;gBAAAvB,QAAA,EAAEoB,IAAI,CAAC3B;cAAI;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAC1C,CAACpB,WAAW,iBACXF,OAAA;gBAAMuC,SAAS,EAAC,WAAW;gBAAAvB,QAAA,EAAEoB,IAAI,CAACb;cAAK;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAC/C,EACA,CAACpB,WAAW,iBAAIF,OAAA;gBAAKuC,SAAS,EAAC;cAAe;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UAAC,GAbHc,IAAI,CAAC5B,IAAI;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcd,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtB,OAAA;MAAKuC,SAAS,EAAC,gBAAgB;MAAAvB,QAAA,GAC5B,CAACd,WAAW,iBACXF,OAAA;QAAKuC,SAAS,EAAC,WAAW;QAAAvB,QAAA,gBACxBhB,OAAA;UAAKuC,SAAS,EAAC,aAAa;UAAAvB,QAAA,eAC1BhB,OAAA;YACEU,KAAK,EAAC,IAAI;YACVC,MAAM,EAAC,IAAI;YACXC,OAAO,EAAC,WAAW;YACnBC,IAAI,EAAC,MAAM;YACXC,MAAM,EAAC,cAAc;YACrBC,WAAW,EAAC,GAAG;YAAAC,QAAA,gBAEfhB,OAAA;cAAMyB,CAAC,EAAC;YAA2C;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3DtB,OAAA;cAAQ0B,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,GAAG;cAACC,CAAC,EAAC;YAAG;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNtB,OAAA;UAAKuC,SAAS,EAAC,WAAW;UAAAvB,QAAA,gBACxBhB,OAAA;YAAKuC,SAAS,EAAC,WAAW;YAAAvB,QAAA,EAAEV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsC;UAAI;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7CtB,OAAA;YAAKuC,SAAS,EAAC,WAAW;YAAAvB,QAAA,EAAEV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgC;UAAI;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAEDtB,OAAA;QAAKuC,SAAS,EAAC,gBAAgB;QAAAvB,QAAA,eAC7BhB,OAAA,CAACH,OAAO;UAAC4C,EAAE,EAAC,UAAU;UAACF,SAAS,EAAC,eAAe;UAACI,KAAK,EAAC,SAAS;UAAA3B,QAAA,gBAC9DhB,OAAA;YACEU,KAAK,EAAC,IAAI;YACVC,MAAM,EAAC,IAAI;YACXC,OAAO,EAAC,WAAW;YACnBC,IAAI,EAAC,MAAM;YACXC,MAAM,EAAC,cAAc;YACrBC,WAAW,EAAC,GAAG;YAAAC,QAAA,gBAEfhB,OAAA;cAAMyB,CAAC,EAAC;YAA2C;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3DtB,OAAA;cAAQ0B,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,GAAG;cAACC,CAAC,EAAC;YAAG;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,EACL,CAACpB,WAAW,iBAAIF,OAAA;YAAAgB,QAAA,EAAM;UAAO;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ,CAAC;AAACjB,EAAA,CAvMIJ,OAAO;EAAA,QACMH,OAAO;AAAA;AAAA+C,EAAA,GADpB5C,OAAO;AAyMb,eAAeA,OAAO;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}