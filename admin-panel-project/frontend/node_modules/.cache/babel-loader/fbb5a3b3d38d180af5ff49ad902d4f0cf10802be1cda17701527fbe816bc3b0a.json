{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/components/Header.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { NavLink, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = () => {\n  _s();\n  var _user$name, _user$name$charAt;\n  const {\n    user,\n    logout\n  } = useAuth();\n  const navigate = useNavigate();\n  const [showUserMenu, setShowUserMenu] = useState(false);\n  const menuItems = [{\n    path: '/dashboard',\n    icon: '📊',\n    label: 'Dashboard',\n    roles: ['superadmin', 'admin', 'agent']\n  }, {\n    path: '/users',\n    icon: '👥',\n    label: 'Users',\n    roles: ['superadmin', 'admin']\n  }, {\n    path: '/analytics',\n    icon: '📈',\n    label: 'Analytics',\n    roles: ['superadmin', 'admin', 'agent']\n  }, {\n    path: '/reports',\n    icon: '📋',\n    label: 'Reports',\n    roles: ['superadmin', 'admin']\n  }, {\n    path: '/settings',\n    icon: '⚙️',\n    label: 'Settings',\n    roles: ['superadmin', 'admin']\n  }];\n  const handleLogout = () => {\n    logout();\n    navigate('/login');\n  };\n  const handleProfile = () => {\n    navigate('/profile');\n    setShowUserMenu(false);\n  };\n  const handleSettings = () => {\n    navigate('/settings');\n    setShowUserMenu(false);\n  };\n\n  // Filter menu items based on user role\n  const filteredMenuItems = menuItems.filter(item => item.roles.includes(user === null || user === void 0 ? void 0 : user.role));\n  const getRoleColor = role => {\n    switch (role) {\n      case 'superadmin':\n        return '#ef4444';\n      case 'admin':\n        return '#3b82f6';\n      case 'agent':\n        return '#10b981';\n      default:\n        return '#64748b';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"header\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-logo\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo-icon\",\n          children: \"AP\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"logo-text\",\n          children: \"Admin Panel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"header-nav\",\n        children: filteredMenuItems.map(item => /*#__PURE__*/_jsxDEV(NavLink, {\n          to: item.path,\n          className: ({\n            isActive\n          }) => `nav-item ${isActive ? 'active' : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"nav-icon\",\n            children: item.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"nav-label\",\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 15\n          }, this)]\n        }, item.path, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-user\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-profile\",\n          onClick: () => setShowUserMenu(!showUserMenu),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-avatar\",\n            children: user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : (_user$name$charAt = _user$name.charAt(0)) === null || _user$name$charAt === void 0 ? void 0 : _user$name$charAt.toUpperCase()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"user-name\",\n              children: user === null || user === void 0 ? void 0 : user.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"user-role\",\n              style: {\n                color: getRoleColor(user === null || user === void 0 ? void 0 : user.role)\n              },\n              children: user === null || user === void 0 ? void 0 : user.role\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"dropdown-arrow\",\n            children: \"\\u25BC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), showUserMenu && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-dropdown\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"dropdown-item\",\n            onClick: handleProfile,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"dropdown-icon\",\n              children: \"\\uD83D\\uDC64\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 17\n            }, this), \"Profile\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"dropdown-item\",\n            onClick: handleSettings,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"dropdown-icon\",\n              children: \"\\u2699\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 17\n            }, this), \"Settings\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"dropdown-divider\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"dropdown-item logout\",\n            onClick: handleLogout,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"dropdown-icon\",\n              children: \"\\uD83D\\uDEAA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 17\n            }, this), \"Logout\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this), showUserMenu && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dropdown-overlay\",\n      onClick: () => setShowUserMenu(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"l/dNgIzDD5aHvjW6n1QIsKhZ5BI=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "NavLink", "useNavigate", "useAuth", "jsxDEV", "_jsxDEV", "Header", "_s", "_user$name", "_user$name$charAt", "user", "logout", "navigate", "showUserMenu", "setShowUserMenu", "menuItems", "path", "icon", "label", "roles", "handleLogout", "handleProfile", "handleSettings", "filteredMenuItems", "filter", "item", "includes", "role", "getRoleColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "to", "isActive", "onClick", "name", "char<PERSON>t", "toUpperCase", "style", "color", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/components/Header.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { NavLink, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\n\nconst Header = () => {\n  const { user, logout } = useAuth();\n  const navigate = useNavigate();\n  const [showUserMenu, setShowUserMenu] = useState(false);\n\n  const menuItems = [\n    {\n      path: '/dashboard',\n      icon: '📊',\n      label: 'Dashboard',\n      roles: ['superadmin', 'admin', 'agent'],\n    },\n    {\n      path: '/users',\n      icon: '👥',\n      label: 'Users',\n      roles: ['superadmin', 'admin'],\n    },\n    {\n      path: '/analytics',\n      icon: '📈',\n      label: 'Analytics',\n      roles: ['superadmin', 'admin', 'agent'],\n    },\n    {\n      path: '/reports',\n      icon: '📋',\n      label: 'Reports',\n      roles: ['superadmin', 'admin'],\n    },\n    {\n      path: '/settings',\n      icon: '⚙️',\n      label: 'Settings',\n      roles: ['superadmin', 'admin'],\n    },\n  ];\n\n  const handleLogout = () => {\n    logout();\n    navigate('/login');\n  };\n\n  const handleProfile = () => {\n    navigate('/profile');\n    setShowUserMenu(false);\n  };\n\n  const handleSettings = () => {\n    navigate('/settings');\n    setShowUserMenu(false);\n  };\n\n  // Filter menu items based on user role\n  const filteredMenuItems = menuItems.filter(item => \n    item.roles.includes(user?.role)\n  );\n\n  const getRoleColor = (role) => {\n    switch (role) {\n      case 'superadmin':\n        return '#ef4444';\n      case 'admin':\n        return '#3b82f6';\n      case 'agent':\n        return '#10b981';\n      default:\n        return '#64748b';\n    }\n  };\n\n  return (\n    <header className=\"header\">\n      <div className=\"header-container\">\n        {/* Logo */}\n        <div className=\"header-logo\">\n          <div className=\"logo-icon\">AP</div>\n          <span className=\"logo-text\">Admin Panel</span>\n        </div>\n\n        {/* Navigation Menu */}\n        <nav className=\"header-nav\">\n          {filteredMenuItems.map((item) => (\n            <NavLink\n              key={item.path}\n              to={item.path}\n              className={({ isActive }) => \n                `nav-item ${isActive ? 'active' : ''}`\n              }\n            >\n              <span className=\"nav-icon\">{item.icon}</span>\n              <span className=\"nav-label\">{item.label}</span>\n            </NavLink>\n          ))}\n        </nav>\n\n        {/* User Menu */}\n        <div className=\"header-user\">\n          <div \n            className=\"user-profile\"\n            onClick={() => setShowUserMenu(!showUserMenu)}\n          >\n            <div className=\"user-avatar\">\n              {user?.name?.charAt(0)?.toUpperCase()}\n            </div>\n            <div className=\"user-info\">\n              <div className=\"user-name\">{user?.name}</div>\n              <div \n                className=\"user-role\"\n                style={{ color: getRoleColor(user?.role) }}\n              >\n                {user?.role}\n              </div>\n            </div>\n            <div className=\"dropdown-arrow\">▼</div>\n          </div>\n\n          {/* Dropdown Menu */}\n          {showUserMenu && (\n            <div className=\"user-dropdown\">\n              <button \n                className=\"dropdown-item\"\n                onClick={handleProfile}\n              >\n                <span className=\"dropdown-icon\">👤</span>\n                Profile\n              </button>\n              <button \n                className=\"dropdown-item\"\n                onClick={handleSettings}\n              >\n                <span className=\"dropdown-icon\">⚙️</span>\n                Settings\n              </button>\n              <div className=\"dropdown-divider\"></div>\n              <button \n                className=\"dropdown-item logout\"\n                onClick={handleLogout}\n              >\n                <span className=\"dropdown-icon\">🚪</span>\n                Logout\n              </button>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Click outside to close dropdown */}\n      {showUserMenu && (\n        <div \n          className=\"dropdown-overlay\"\n          onClick={() => setShowUserMenu(false)}\n        ></div>\n      )}\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,EAAEC,WAAW,QAAQ,kBAAkB;AACvD,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,UAAA,EAAAC,iBAAA;EACnB,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGR,OAAO,CAAC,CAAC;EAClC,MAAMS,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACW,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMe,SAAS,GAAG,CAChB;IACEC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE,CAAC,YAAY,EAAE,OAAO,EAAE,OAAO;EACxC,CAAC,EACD;IACEH,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,CAAC,YAAY,EAAE,OAAO;EAC/B,CAAC,EACD;IACEH,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE,CAAC,YAAY,EAAE,OAAO,EAAE,OAAO;EACxC,CAAC,EACD;IACEH,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,CAAC,YAAY,EAAE,OAAO;EAC/B,CAAC,EACD;IACEH,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,CAAC,YAAY,EAAE,OAAO;EAC/B,CAAC,CACF;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzBT,MAAM,CAAC,CAAC;IACRC,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMS,aAAa,GAAGA,CAAA,KAAM;IAC1BT,QAAQ,CAAC,UAAU,CAAC;IACpBE,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMQ,cAAc,GAAGA,CAAA,KAAM;IAC3BV,QAAQ,CAAC,WAAW,CAAC;IACrBE,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;;EAED;EACA,MAAMS,iBAAiB,GAAGR,SAAS,CAACS,MAAM,CAACC,IAAI,IAC7CA,IAAI,CAACN,KAAK,CAACO,QAAQ,CAAChB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,IAAI,CAChC,CAAC;EAED,MAAMC,YAAY,GAAID,IAAI,IAAK;IAC7B,QAAQA,IAAI;MACV,KAAK,YAAY;QACf,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,oBACEtB,OAAA;IAAQwB,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBACxBzB,OAAA;MAAKwB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAE/BzB,OAAA;QAAKwB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BzB,OAAA;UAAKwB,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnC7B,OAAA;UAAMwB,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eAGN7B,OAAA;QAAKwB,SAAS,EAAC,YAAY;QAAAC,QAAA,EACxBP,iBAAiB,CAACY,GAAG,CAAEV,IAAI,iBAC1BpB,OAAA,CAACJ,OAAO;UAENmC,EAAE,EAAEX,IAAI,CAACT,IAAK;UACda,SAAS,EAAEA,CAAC;YAAEQ;UAAS,CAAC,KACtB,YAAYA,QAAQ,GAAG,QAAQ,GAAG,EAAE,EACrC;UAAAP,QAAA,gBAEDzB,OAAA;YAAMwB,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAEL,IAAI,CAACR;UAAI;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7C7B,OAAA;YAAMwB,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAEL,IAAI,CAACP;UAAK;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAP1CT,IAAI,CAACT,IAAI;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQP,CACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN7B,OAAA;QAAKwB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BzB,OAAA;UACEwB,SAAS,EAAC,cAAc;UACxBS,OAAO,EAAEA,CAAA,KAAMxB,eAAe,CAAC,CAACD,YAAY,CAAE;UAAAiB,QAAA,gBAE9CzB,OAAA;YAAKwB,SAAS,EAAC,aAAa;YAAAC,QAAA,EACzBpB,IAAI,aAAJA,IAAI,wBAAAF,UAAA,GAAJE,IAAI,CAAE6B,IAAI,cAAA/B,UAAA,wBAAAC,iBAAA,GAAVD,UAAA,CAAYgC,MAAM,CAAC,CAAC,CAAC,cAAA/B,iBAAA,uBAArBA,iBAAA,CAAuBgC,WAAW,CAAC;UAAC;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACN7B,OAAA;YAAKwB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBzB,OAAA;cAAKwB,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAEpB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6B;YAAI;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7C7B,OAAA;cACEwB,SAAS,EAAC,WAAW;cACrBa,KAAK,EAAE;gBAAEC,KAAK,EAAEf,YAAY,CAAClB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,IAAI;cAAE,CAAE;cAAAG,QAAA,EAE1CpB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN7B,OAAA;YAAKwB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,EAGLrB,YAAY,iBACXR,OAAA;UAAKwB,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BzB,OAAA;YACEwB,SAAS,EAAC,eAAe;YACzBS,OAAO,EAAEjB,aAAc;YAAAS,QAAA,gBAEvBzB,OAAA;cAAMwB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,WAE3C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7B,OAAA;YACEwB,SAAS,EAAC,eAAe;YACzBS,OAAO,EAAEhB,cAAe;YAAAQ,QAAA,gBAExBzB,OAAA;cAAMwB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,YAE3C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7B,OAAA;YAAKwB,SAAS,EAAC;UAAkB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxC7B,OAAA;YACEwB,SAAS,EAAC,sBAAsB;YAChCS,OAAO,EAAElB,YAAa;YAAAU,QAAA,gBAEtBzB,OAAA;cAAMwB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,UAE3C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLrB,YAAY,iBACXR,OAAA;MACEwB,SAAS,EAAC,kBAAkB;MAC5BS,OAAO,EAAEA,CAAA,KAAMxB,eAAe,CAAC,KAAK;IAAE;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAAC3B,EAAA,CA5JID,MAAM;EAAA,QACeH,OAAO,EACfD,WAAW;AAAA;AAAA0C,EAAA,GAFxBtC,MAAM;AA8JZ,eAAeA,MAAM;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}