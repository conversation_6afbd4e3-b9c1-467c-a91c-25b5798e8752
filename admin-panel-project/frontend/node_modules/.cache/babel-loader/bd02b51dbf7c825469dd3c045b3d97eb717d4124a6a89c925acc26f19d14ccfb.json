{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/components/Header.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useAuth } from \"../context/AuthContext\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = ({\n  onToggleSidebar\n}) => {\n  _s();\n  var _user$name, _user$name$charAt;\n  const {\n    user,\n    logout\n  } = useAuth();\n  const navigate = useNavigate();\n  const [showUserMenu, setShowUserMenu] = useState(false);\n  const handleLogout = () => {\n    logout();\n    navigate(\"/login\");\n  };\n  const handleProfile = () => {\n    navigate(\"/profile\");\n    setShowUserMenu(false);\n  };\n  const handleSettings = () => {\n    navigate(\"/settings\");\n    setShowUserMenu(false);\n  };\n\n  // Filter menu items based on user role\n  const filteredMenuItems = menuItems.filter(item => item.roles.includes(user === null || user === void 0 ? void 0 : user.role));\n  const getRoleColor = role => {\n    switch (role) {\n      case \"superadmin\":\n        return \"#ef4444\";\n      case \"admin\":\n        return \"#3b82f6\";\n      case \"agent\":\n        return \"#10b981\";\n      default:\n        return \"#64748b\";\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"header\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-logo\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo-icon\",\n          children: \"AP\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"logo-text\",\n          children: \"Admin Panel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"header-nav\",\n        children: filteredMenuItems.map(item => /*#__PURE__*/_jsxDEV(NavLink, {\n          to: item.path,\n          className: ({\n            isActive\n          }) => `nav-item ${isActive ? \"active\" : \"\"}`,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"nav-icon\",\n            children: item.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"nav-label\",\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 15\n          }, this)]\n        }, item.path, true, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-user\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-profile\",\n          onClick: () => setShowUserMenu(!showUserMenu),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-avatar\",\n            children: user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : (_user$name$charAt = _user$name.charAt(0)) === null || _user$name$charAt === void 0 ? void 0 : _user$name$charAt.toUpperCase()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"user-name\",\n              children: user === null || user === void 0 ? void 0 : user.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"user-role\",\n              style: {\n                color: getRoleColor(user === null || user === void 0 ? void 0 : user.role)\n              },\n              children: user === null || user === void 0 ? void 0 : user.role\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"dropdown-arrow\",\n            children: \"\\u25BC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), showUserMenu && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-dropdown\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"dropdown-item\",\n            onClick: handleProfile,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"dropdown-icon\",\n              children: \"\\uD83D\\uDC64\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 17\n            }, this), \"Profile\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"dropdown-item\",\n            onClick: handleSettings,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"dropdown-icon\",\n              children: \"\\u2699\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 17\n            }, this), \"Settings\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"dropdown-divider\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"dropdown-item logout\",\n            onClick: handleLogout,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"dropdown-icon\",\n              children: \"\\uD83D\\uDEAA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 17\n            }, this), \"Logout\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), showUserMenu && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dropdown-overlay\",\n      onClick: () => setShowUserMenu(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"l/dNgIzDD5aHvjW6n1QIsKhZ5BI=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useAuth", "jsxDEV", "_jsxDEV", "Header", "onToggleSidebar", "_s", "_user$name", "_user$name$charAt", "user", "logout", "navigate", "showUserMenu", "setShowUserMenu", "handleLogout", "handleProfile", "handleSettings", "filteredMenuItems", "menuItems", "filter", "item", "roles", "includes", "role", "getRoleColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "NavLink", "to", "path", "isActive", "icon", "label", "onClick", "name", "char<PERSON>t", "toUpperCase", "style", "color", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/components/Header.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useAuth } from \"../context/AuthContext\";\n\nconst Header = ({ onToggleSidebar }) => {\n  const { user, logout } = useAuth();\n  const navigate = useNavigate();\n  const [showUserMenu, setShowUserMenu] = useState(false);\n\n  const handleLogout = () => {\n    logout();\n    navigate(\"/login\");\n  };\n\n  const handleProfile = () => {\n    navigate(\"/profile\");\n    setShowUserMenu(false);\n  };\n\n  const handleSettings = () => {\n    navigate(\"/settings\");\n    setShowUserMenu(false);\n  };\n\n  // Filter menu items based on user role\n  const filteredMenuItems = menuItems.filter((item) =>\n    item.roles.includes(user?.role)\n  );\n\n  const getRoleColor = (role) => {\n    switch (role) {\n      case \"superadmin\":\n        return \"#ef4444\";\n      case \"admin\":\n        return \"#3b82f6\";\n      case \"agent\":\n        return \"#10b981\";\n      default:\n        return \"#64748b\";\n    }\n  };\n\n  return (\n    <header className=\"header\">\n      <div className=\"header-container\">\n        {/* Logo */}\n        <div className=\"header-logo\">\n          <div className=\"logo-icon\">AP</div>\n          <span className=\"logo-text\">Admin Panel</span>\n        </div>\n\n        {/* Navigation Menu */}\n        <nav className=\"header-nav\">\n          {filteredMenuItems.map((item) => (\n            <NavLink\n              key={item.path}\n              to={item.path}\n              className={({ isActive }) =>\n                `nav-item ${isActive ? \"active\" : \"\"}`\n              }\n            >\n              <span className=\"nav-icon\">{item.icon}</span>\n              <span className=\"nav-label\">{item.label}</span>\n            </NavLink>\n          ))}\n        </nav>\n\n        {/* User Menu */}\n        <div className=\"header-user\">\n          <div\n            className=\"user-profile\"\n            onClick={() => setShowUserMenu(!showUserMenu)}\n          >\n            <div className=\"user-avatar\">\n              {user?.name?.charAt(0)?.toUpperCase()}\n            </div>\n            <div className=\"user-info\">\n              <div className=\"user-name\">{user?.name}</div>\n              <div\n                className=\"user-role\"\n                style={{ color: getRoleColor(user?.role) }}\n              >\n                {user?.role}\n              </div>\n            </div>\n            <div className=\"dropdown-arrow\">▼</div>\n          </div>\n\n          {/* Dropdown Menu */}\n          {showUserMenu && (\n            <div className=\"user-dropdown\">\n              <button className=\"dropdown-item\" onClick={handleProfile}>\n                <span className=\"dropdown-icon\">👤</span>\n                Profile\n              </button>\n              <button className=\"dropdown-item\" onClick={handleSettings}>\n                <span className=\"dropdown-icon\">⚙️</span>\n                Settings\n              </button>\n              <div className=\"dropdown-divider\"></div>\n              <button className=\"dropdown-item logout\" onClick={handleLogout}>\n                <span className=\"dropdown-icon\">🚪</span>\n                Logout\n              </button>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Click outside to close dropdown */}\n      {showUserMenu && (\n        <div\n          className=\"dropdown-overlay\"\n          onClick={() => setShowUserMenu(false)}\n        ></div>\n      )}\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,MAAM,GAAGA,CAAC;EAAEC;AAAgB,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,UAAA,EAAAC,iBAAA;EACtC,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGT,OAAO,CAAC,CAAC;EAClC,MAAMU,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACY,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMe,YAAY,GAAGA,CAAA,KAAM;IACzBJ,MAAM,CAAC,CAAC;IACRC,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMI,aAAa,GAAGA,CAAA,KAAM;IAC1BJ,QAAQ,CAAC,UAAU,CAAC;IACpBE,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMG,cAAc,GAAGA,CAAA,KAAM;IAC3BL,QAAQ,CAAC,WAAW,CAAC;IACrBE,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;;EAED;EACA,MAAMI,iBAAiB,GAAGC,SAAS,CAACC,MAAM,CAAEC,IAAI,IAC9CA,IAAI,CAACC,KAAK,CAACC,QAAQ,CAACb,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEc,IAAI,CAChC,CAAC;EAED,MAAMC,YAAY,GAAID,IAAI,IAAK;IAC7B,QAAQA,IAAI;MACV,KAAK,YAAY;QACf,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,oBACEpB,OAAA;IAAQsB,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBACxBvB,OAAA;MAAKsB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAE/BvB,OAAA;QAAKsB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BvB,OAAA;UAAKsB,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnC3B,OAAA;UAAMsB,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eAGN3B,OAAA;QAAKsB,SAAS,EAAC,YAAY;QAAAC,QAAA,EACxBT,iBAAiB,CAACc,GAAG,CAAEX,IAAI,iBAC1BjB,OAAA,CAAC6B,OAAO;UAENC,EAAE,EAAEb,IAAI,CAACc,IAAK;UACdT,SAAS,EAAEA,CAAC;YAAEU;UAAS,CAAC,KACtB,YAAYA,QAAQ,GAAG,QAAQ,GAAG,EAAE,EACrC;UAAAT,QAAA,gBAEDvB,OAAA;YAAMsB,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAEN,IAAI,CAACgB;UAAI;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7C3B,OAAA;YAAMsB,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAEN,IAAI,CAACiB;UAAK;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAP1CV,IAAI,CAACc,IAAI;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQP,CACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN3B,OAAA;QAAKsB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BvB,OAAA;UACEsB,SAAS,EAAC,cAAc;UACxBa,OAAO,EAAEA,CAAA,KAAMzB,eAAe,CAAC,CAACD,YAAY,CAAE;UAAAc,QAAA,gBAE9CvB,OAAA;YAAKsB,SAAS,EAAC,aAAa;YAAAC,QAAA,EACzBjB,IAAI,aAAJA,IAAI,wBAAAF,UAAA,GAAJE,IAAI,CAAE8B,IAAI,cAAAhC,UAAA,wBAAAC,iBAAA,GAAVD,UAAA,CAAYiC,MAAM,CAAC,CAAC,CAAC,cAAAhC,iBAAA,uBAArBA,iBAAA,CAAuBiC,WAAW,CAAC;UAAC;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACN3B,OAAA;YAAKsB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBvB,OAAA;cAAKsB,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAEjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B;YAAI;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7C3B,OAAA;cACEsB,SAAS,EAAC,WAAW;cACrBiB,KAAK,EAAE;gBAAEC,KAAK,EAAEnB,YAAY,CAACf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEc,IAAI;cAAE,CAAE;cAAAG,QAAA,EAE1CjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEc;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN3B,OAAA;YAAKsB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,EAGLlB,YAAY,iBACXT,OAAA;UAAKsB,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BvB,OAAA;YAAQsB,SAAS,EAAC,eAAe;YAACa,OAAO,EAAEvB,aAAc;YAAAW,QAAA,gBACvDvB,OAAA;cAAMsB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,WAE3C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3B,OAAA;YAAQsB,SAAS,EAAC,eAAe;YAACa,OAAO,EAAEtB,cAAe;YAAAU,QAAA,gBACxDvB,OAAA;cAAMsB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,YAE3C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3B,OAAA;YAAKsB,SAAS,EAAC;UAAkB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxC3B,OAAA;YAAQsB,SAAS,EAAC,sBAAsB;YAACa,OAAO,EAAExB,YAAa;YAAAY,QAAA,gBAC7DvB,OAAA;cAAMsB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,UAE3C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLlB,YAAY,iBACXT,OAAA;MACEsB,SAAS,EAAC,kBAAkB;MAC5Ba,OAAO,EAAEA,CAAA,KAAMzB,eAAe,CAAC,KAAK;IAAE;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAACxB,EAAA,CAlHIF,MAAM;EAAA,QACeH,OAAO,EACfD,WAAW;AAAA;AAAA4C,EAAA,GAFxBxC,MAAM;AAoHZ,eAAeA,MAAM;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}