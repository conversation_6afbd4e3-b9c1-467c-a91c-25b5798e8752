[{"/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/index.js": "1", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/reportWebVitals.js": "2", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/context/AuthContext.js": "3", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/services/api.js": "4", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Login.jsx": "5", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Dashboard.jsx": "6", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/components/Sidebar.jsx": "7", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Profile.jsx": "8", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Reports.jsx": "9", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Settings.jsx": "10", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Analytics.jsx": "11", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/UserManagement.jsx": "12", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/App.jsx": "13", "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/components/Header.jsx": "14"}, {"size": 517, "mtime": 1749806676269, "results": "15", "hashOfConfig": "16"}, {"size": 362, "mtime": 1749805634052, "results": "17", "hashOfConfig": "16"}, {"size": 5836, "mtime": 1749807471237, "results": "18", "hashOfConfig": "16"}, {"size": 2290, "mtime": 1749805998766, "results": "19", "hashOfConfig": "16"}, {"size": 6151, "mtime": 1749806415791, "results": "20", "hashOfConfig": "16"}, {"size": 18789, "mtime": 1749807933751, "results": "21", "hashOfConfig": "16"}, {"size": 4599, "mtime": 1749809265252, "results": "22", "hashOfConfig": "16"}, {"size": 11009, "mtime": 1749806621762, "results": "23", "hashOfConfig": "16"}, {"size": 2279, "mtime": 1749806554671, "results": "24", "hashOfConfig": "16"}, {"size": 8389, "mtime": 1749806581546, "results": "25", "hashOfConfig": "16"}, {"size": 3100, "mtime": 1749806542114, "results": "26", "hashOfConfig": "16"}, {"size": 11360, "mtime": 1749806523920, "results": "27", "hashOfConfig": "16"}, {"size": 4096, "mtime": 1749809851314, "results": "28", "hashOfConfig": "16"}, {"size": 3497, "mtime": 1749808576607, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "xxxguz", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/index.js", [], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/reportWebVitals.js", [], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/context/AuthContext.js", [], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/services/api.js", [], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Login.jsx", [], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Dashboard.jsx", [], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/components/Sidebar.jsx", [], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Profile.jsx", ["72"], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Reports.jsx", ["73"], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Settings.jsx", ["74"], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/Analytics.jsx", [], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/pages/UserManagement.jsx", ["75"], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/App.jsx", [], [], "/Users/<USER>/Mine/mywork/ownPractice/admin-panel-project/frontend/src/components/Header.jsx", [], [], {"ruleId": "76", "severity": 1, "message": "77", "line": 5, "column": 17, "nodeType": "78", "messageId": "79", "endLine": 5, "endColumn": 27}, {"ruleId": "76", "severity": 1, "message": "80", "line": 5, "column": 11, "nodeType": "78", "messageId": "79", "endLine": 5, "endColumn": 15}, {"ruleId": "76", "severity": 1, "message": "80", "line": 5, "column": 11, "nodeType": "78", "messageId": "79", "endLine": 5, "endColumn": 15}, {"ruleId": "81", "severity": 1, "message": "82", "line": 17, "column": 6, "nodeType": "83", "endLine": 17, "endColumn": 45, "suggestions": "84"}, "no-unused-vars", "'updateUser' is assigned a value but never used.", "Identifier", "unusedVar", "'user' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadUsers'. Either include it or remove the dependency array.", "ArrayExpression", ["85"], {"desc": "86", "fix": "87"}, "Update the dependencies array to be: [currentPage, loadUsers, searchTerm, selectedRole]", {"range": "88", "text": "89"}, [612, 651], "[currentPage, loadUsers, searchTerm, selectedRole]"]