{"name": "admin-panel-backend", "version": "1.0.0", "description": "Professional Admin Panel Backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1", "create-superadmin": "node scripts/createSuperAdmin.js"}, "keywords": ["admin", "panel", "api", "express", "mongodb"], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.2", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.1.10"}}