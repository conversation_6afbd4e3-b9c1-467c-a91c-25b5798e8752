const mongoose = require("mongoose");
const dotenv = require("dotenv");
const User = require("../models/User");

// Load environment variables
dotenv.config();

const createSuperAdmin = async () => {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI);

    console.log("Connected to MongoDB");

    // Check if superadmin already exists
    const existingSuperAdmin = await User.findOne({ role: "superadmin" });

    if (existingSuperAdmin) {
      console.log("Superadmin already exists:");
      console.log(`Name: ${existingSuperAdmin.name}`);
      console.log(`Username: ${existingSuperAdmin.username}`);
      console.log(`Email: ${existingSuperAdmin.email}`);
      process.exit(0);
    }

    // Create superadmin user
    const superAdmin = await User.create({
      name: "Super Administrator",
      username: "superadmin",
      email: "<EMAIL>",
      password: "admin123",
      role: "superadmin",
      isActive: true,
    });

    console.log("Superadmin created successfully:");
    console.log(`Name: ${superAdmin.name}`);
    console.log(`Username: ${superAdmin.username}`);
    console.log(`Email: ${superAdmin.email}`);
    console.log(`Password: admin123`);
    console.log("\nPlease change the password after first login!");
  } catch (error) {
    console.error("Error creating superadmin:", error.message);
  } finally {
    mongoose.connection.close();
    process.exit(0);
  }
};

createSuperAdmin();
